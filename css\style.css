/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1.1em;
    color: #666;
    margin-bottom: 15px;
}

.free-badge {
    display: inline-block;
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-weight: bold;
    font-size: 0.9em;
}

/* 上传区域样式 */
.upload-section {
    margin-bottom: 40px;
}

.upload-area {
    background: rgba(255, 255, 255, 0.95);
    border: 3px dashed #ddd;
    border-radius: 20px;
    padding: 60px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #667eea;
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.upload-area.dragover {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
}

.upload-icon {
    font-size: 4em;
    margin-bottom: 20px;
}

.upload-content h3 {
    font-size: 1.5em;
    margin-bottom: 10px;
    color: #333;
}

.upload-content p {
    color: #666;
    margin-bottom: 20px;
}

.upload-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1em;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

/* 状态显示样式 */
.status-section {
    margin-bottom: 40px;
}

.status-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.status-icon {
    font-size: 3em;
    margin-bottom: 20px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.status-card h3 {
    font-size: 1.5em;
    margin-bottom: 20px;
    color: #333;
}

.progress-container {
    margin: 20px 0;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #4CAF50, #45a049);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-text {
    font-weight: bold;
    color: #4CAF50;
}

.status-description {
    color: #666;
    font-size: 0.9em;
}

/* 主工作区样式 */
.main-workspace {
    margin-bottom: 40px;
}

.workspace-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.floorplan-panel,
.result-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.panel-header {
    margin-bottom: 20px;
    text-align: center;
}

.panel-header h3 {
    font-size: 1.3em;
    margin-bottom: 5px;
    color: #333;
}

.panel-header p {
    color: #666;
    font-size: 0.9em;
}

.canvas-container {
    position: relative;
    background: #f8f9fa;
    border-radius: 15px;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

#floorplanCanvas,
#resultCanvas {
    max-width: 100%;
    max-height: 400px;
    border-radius: 15px;
    cursor: pointer;
}

.placeholder {
    text-align: center;
    color: #999;
}

.placeholder-icon {
    font-size: 3em;
    margin-bottom: 15px;
}

/* 房间图例样式 */
.room-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 5px 12px;
    background: #f8f9fa;
    border-radius: 20px;
    font-size: 0.9em;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 操作按钮样式 */
.result-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.action-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 25px;
    font-size: 0.9em;
    cursor: pointer;
    transition: all 0.3s ease;
}

.save-btn {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
}

.regenerate-btn {
    background: linear-gradient(45deg, #FF9800, #F57C00);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

/* 使用说明样式 */
.instructions {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.instructions h3 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 1.5em;
    color: #333;
}

.instruction-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
}

.step {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.step-number {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    flex-shrink: 0;
}

.step-content h4 {
    margin-bottom: 8px;
    color: #333;
}

.step-content p {
    color: #666;
    font-size: 0.9em;
}

/* 免费信息样式 */
.free-info {
    margin-bottom: 30px;
}

.free-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.free-card h3 {
    text-align: center;
    margin-bottom: 20px;
    color: #4CAF50;
    font-size: 1.3em;
}

.free-card ul {
    list-style: none;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.free-card li {
    color: #333;
    font-size: 0.95em;
}

/* 页脚样式 */
.footer {
    text-align: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .workspace-grid {
        grid-template-columns: 1fr;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .instruction-steps {
        grid-template-columns: 1fr;
    }
    
    .free-card ul {
        grid-template-columns: 1fr;
    }
    
    .container {
        padding: 15px;
    }
}

/* 隐藏类 */
.hidden {
    display: none !important;
}
