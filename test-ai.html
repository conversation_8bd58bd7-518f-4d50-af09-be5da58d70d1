<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI装修效果图测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
        }
        
        .test-section h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 10px 10px 0;
            transition: transform 0.3s ease;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            min-height: 100px;
        }
        
        .ai-image {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI装修效果图生成测试</h1>
        
        <div class="test-section">
            <h3>🎯 测试1: Pollinations AI服务</h3>
            <p>测试免费的Pollinations AI图像生成服务</p>
            <button onclick="testPollinationsAI()">测试Pollinations AI</button>
            <div id="pollinations-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>🏠 测试2: 客厅装修效果图</h3>
            <p>生成现代客厅装修效果图</p>
            <button onclick="testLivingRoom()">生成客厅效果图</button>
            <div id="living-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>🛏️ 测试3: 卧室装修效果图</h3>
            <p>生成温馨卧室装修效果图</p>
            <button onclick="testBedroom()">生成卧室效果图</button>
            <div id="bedroom-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>🍳 测试4: 厨房装修效果图</h3>
            <p>生成现代厨房装修效果图</p>
            <button onclick="testKitchen()">生成厨房效果图</button>
            <div id="kitchen-result" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 测试结果统计</h3>
            <div id="test-stats">
                <p>成功: <span id="success-count">0</span></p>
                <p>失败: <span id="error-count">0</span></p>
                <p>总计: <span id="total-count">0</span></p>
            </div>
        </div>
    </div>

    <script>
        let successCount = 0;
        let errorCount = 0;
        let totalCount = 0;
        
        function updateStats() {
            document.getElementById('success-count').textContent = successCount;
            document.getElementById('error-count').textContent = errorCount;
            document.getElementById('total-count').textContent = totalCount;
        }
        
        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function showImage(elementId, imageUrl, title) {
            const element = document.getElementById(elementId);
            element.innerHTML = `
                <div class="status success">✅ ${title}生成成功！</div>
                <img src="${imageUrl}" alt="${title}" class="ai-image" />
                <p><small>图片URL: ${imageUrl}</small></p>
            `;
        }
        
        async function testPollinationsAI() {
            totalCount++;
            showStatus('pollinations-result', '🔄 正在测试Pollinations AI服务...', 'loading');
            
            try {
                const prompt = encodeURIComponent('modern living room interior design, comfortable sofa, coffee table, warm lighting, high quality, photorealistic');
                const imageUrl = `https://image.pollinations.ai/prompt/${prompt}?width=512&height=512&seed=${Math.floor(Math.random() * 1000000)}`;
                
                // 测试图片是否可以加载
                const img = new Image();
                img.onload = () => {
                    successCount++;
                    updateStats();
                    showImage('pollinations-result', imageUrl, 'Pollinations AI客厅效果图');
                };
                img.onerror = () => {
                    errorCount++;
                    updateStats();
                    showStatus('pollinations-result', '❌ Pollinations AI服务不可用', 'error');
                };
                img.src = imageUrl;
                
                // 设置超时
                setTimeout(() => {
                    if (!img.complete) {
                        errorCount++;
                        updateStats();
                        showStatus('pollinations-result', '⏰ Pollinations AI服务超时', 'error');
                    }
                }, 10000);
                
            } catch (error) {
                errorCount++;
                updateStats();
                showStatus('pollinations-result', `❌ 测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testLivingRoom() {
            totalCount++;
            showStatus('living-result', '🔄 正在生成客厅装修效果图...', 'loading');
            
            try {
                const prompt = encodeURIComponent('luxury modern living room interior, elegant sofa set, marble coffee table, 75 inch smart TV on wall, ambient lighting, minimalist contemporary design, hardwood floors, large windows, plants, warm atmosphere, professional interior photography, ultra realistic, 4k quality');
                const imageUrl = `https://image.pollinations.ai/prompt/${prompt}?width=512&height=512&seed=${Math.floor(Math.random() * 1000000)}`;
                
                const img = new Image();
                img.onload = () => {
                    successCount++;
                    updateStats();
                    showImage('living-result', imageUrl, '豪华现代客厅');
                };
                img.onerror = () => {
                    errorCount++;
                    updateStats();
                    showStatus('living-result', '❌ 客厅效果图生成失败', 'error');
                };
                img.src = imageUrl;
                
            } catch (error) {
                errorCount++;
                updateStats();
                showStatus('living-result', `❌ 生成失败: ${error.message}`, 'error');
            }
        }
        
        async function testBedroom() {
            totalCount++;
            showStatus('bedroom-result', '🔄 正在生成卧室装修效果图...', 'loading');
            
            try {
                const prompt = encodeURIComponent('beautiful modern bedroom interior, king size bed with white bedding, wooden nightstands, built-in wardrobe, soft warm lighting, cozy atmosphere, carpet, curtains, minimalist design, professional interior photography, ultra realistic, 4k quality');
                const imageUrl = `https://image.pollinations.ai/prompt/${prompt}?width=512&height=512&seed=${Math.floor(Math.random() * 1000000)}`;
                
                const img = new Image();
                img.onload = () => {
                    successCount++;
                    updateStats();
                    showImage('bedroom-result', imageUrl, '温馨现代卧室');
                };
                img.onerror = () => {
                    errorCount++;
                    updateStats();
                    showStatus('bedroom-result', '❌ 卧室效果图生成失败', 'error');
                };
                img.src = imageUrl;
                
            } catch (error) {
                errorCount++;
                updateStats();
                showStatus('bedroom-result', `❌ 生成失败: ${error.message}`, 'error');
            }
        }
        
        async function testKitchen() {
            totalCount++;
            showStatus('kitchen-result', '🔄 正在生成厨房装修效果图...', 'loading');
            
            try {
                const prompt = encodeURIComponent('stunning modern kitchen interior, white cabinets, quartz countertops, stainless steel appliances, kitchen island, pendant lights, backsplash tiles, hardwood floors, clean contemporary design, professional interior photography, ultra realistic, 4k quality');
                const imageUrl = `https://image.pollinations.ai/prompt/${prompt}?width=512&height=512&seed=${Math.floor(Math.random() * 1000000)}`;
                
                const img = new Image();
                img.onload = () => {
                    successCount++;
                    updateStats();
                    showImage('kitchen-result', imageUrl, '现代时尚厨房');
                };
                img.onerror = () => {
                    errorCount++;
                    updateStats();
                    showStatus('kitchen-result', '❌ 厨房效果图生成失败', 'error');
                };
                img.src = imageUrl;
                
            } catch (error) {
                errorCount++;
                updateStats();
                showStatus('kitchen-result', `❌ 生成失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
