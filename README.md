# 🏠 AI智能装修设计工具

一个基于计算机视觉和AI技术的智能装修设计应用，能够自动识别房型图并生成专业级装修效果图。

## 🎯 项目特色

### 🤖 真实AI装修效果图
- **多AI服务支持** - 集成Pollinations、Craiyon等免费AI服务
- **照片级质量** - 生成接近真实装修照片的高质量效果图
- **专业提示词** - 优化的AI提示词确保生成专业装修设计
- **智能降级** - AI服务不可用时自动使用高质量本地生成

### 🖼️ 智能房间识别
- **自动识别** - 上传房型图自动识别不同房间
- **颜色标注** - 为不同房间类型分配专属颜色
- **精确定位** - 点击任意房间区域即可生成对应效果图

### 🏠 多房间类型支持
- 🛋️ **客厅** - 现代简约、豪华欧式等多种风格
- 🛏️ **卧室** - 温馨舒适、简约现代等设计
- 🍳 **厨房** - 开放式、一字型、L型等布局
- 🚿 **卫生间** - 干湿分离、现代简约等设计
- 🌿 **阳台** - 休闲花园、储物空间等功能

## ✨ 功能特色

- 🤖 **真实AI生成** - 集成多种免费AI服务，生成照片级装修效果图
- 🖼️ **智能房间识别** - 自动识别房型图中的不同房间
- 🎨 **颜色标注系统** - 为不同房间类型分配专属颜色
- 🏠 **多房间支持** - 客厅、卧室、厨房、卫生间、阳台等
- 📱 **响应式设计** - 完美适配桌面端和移动端
- ⚡ **即时生成** - 点击房间立即生成装修效果图
- 💾 **一键保存** - 支持下载高清效果图
- 🔄 **渐进式降级** - AI服务不可用时自动使用高质量本地生成

## 🚀 快速开始

### 1. 直接使用（推荐）
```bash
# 克隆项目
git clone [项目地址]

# 打开应用
open index.html
```

### 2. AI增强配置（可选）
查看 `ai-config.html` 了解如何配置免费AI服务获得更佳效果。

## 🛠️ 技术架构

### 前端技术
- **HTML5 Canvas** - 图像处理和效果图绘制
- **JavaScript ES6+** - 现代JavaScript特性
- **CSS3** - 响应式设计和动画效果
- **Web APIs** - File API、Canvas API等

### AI服务集成
- **Pollinations AI** - 免费AI图像生成服务
- **Craiyon** - 免费DALL-E替代品
- **Stable Diffusion** - 在线和本地部署支持
- **渐进式降级** - 确保服务可用性

### 核心算法
- **房间识别** - 基于颜色和区域的智能识别
- **效果图生成** - AI生成 + 高质量Canvas绘制
- **图像处理** - 实时图像分析和处理

## 📁 项目结构

```
├── index.html              # 主应用页面
├── ai-config.html          # AI配置指南页面
├── test-ai.html           # AI功能测试页面
├── css/
│   └── style.css          # 样式文件
├── js/
│   └── main.js            # 主要JavaScript逻辑
├── AI_CONFIG.md           # AI配置详细说明
└── README.md              # 项目说明文档
```

## 🎨 使用方法

### 基础使用
1. **上传房型图** - 支持JPG、PNG、BMP格式
2. **查看识别结果** - 系统自动识别并标注不同房间
3. **点击生成效果图** - 点击任意房间区域
4. **保存效果图** - 点击保存按钮下载高清图片

### AI增强使用
1. **配置AI服务** - 参考 `ai-config.html` 配置免费API
2. **享受AI效果** - 获得照片级真实装修效果图
3. **多种风格** - AI自动生成多样化设计风格

## 🔧 AI服务配置

### 方案1: Pollinations AI（推荐）
- ✅ 完全免费，无需注册
- ✅ 即时生成，质量优秀
- ✅ 无API限制

### 方案2: Craiyon AI
- ✅ 免费使用
- ✅ DALL-E同等质量
- ⚠️ 生成速度较慢

### 方案3: 本地Stable Diffusion
- ✅ 完全免费，无限制
- ✅ 最高质量
- ⚠️ 需要本地安装

详细配置说明请查看 `AI_CONFIG.md` 文件。

## 🌟 项目亮点

### 零成本方案
- 所有AI服务均为免费
- 无需付费API密钥
- 本地生成作为备用方案

### 用户体验优先
- 渐进式加载和降级
- 友好的错误处理
- 直观的操作界面

### 技术创新
- 多AI服务智能切换
- 高质量Canvas绘制备用
- 响应式设计适配

## 📊 性能特点

- ⚡ **快速响应** - 平均生成时间10-30秒
- 🎯 **高成功率** - 多重备用方案确保可用性
- 📱 **跨平台** - 支持所有现代浏览器
- 💾 **轻量级** - 无需安装，即开即用

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

### 开发环境
- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 本地HTTP服务器（可选，用于测试）

### 贡献方向
- 新的AI服务集成
- 更多房间类型支持
- 界面优化和新功能
- 性能优化和bug修复

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🙏 致谢

- Pollinations AI - 提供免费AI图像生成服务
- Craiyon - 免费DALL-E替代方案
- Stable Diffusion - 开源AI图像生成模型

---

**🎉 立即体验：打开 `index.html` 开始您的AI装修设计之旅！**
