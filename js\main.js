// 主应用程序类
class FloorplanDesigner {
    constructor() {
        this.currentImage = null;
        this.detectedRooms = [];
        this.selectedRoom = null;
        this.isProcessing = false;
        
        // 房间类型配置
        this.roomTypes = {
            1: { name: '客厅', color: '#4A90E2', type: 'living_room' },
            2: { name: '卧室', color: '#7ED321', type: 'bedroom' },
            3: { name: '厨房', color: '#F5A623', type: 'kitchen' },
            4: { name: '卫生间', color: '#BD10E0', type: 'bathroom' },
            5: { name: '阳台', color: '#FF6B35', type: 'balcony' },
            6: { name: '书房', color: '#8B4513', type: 'study' }
        };
        
        this.initializeApp();
    }
    
    // 初始化应用
    initializeApp() {
        console.log('🏠 房型智能装修设计系统启动中...');
        this.setupEventListeners();
        this.setupCanvases();
        this.showWelcomeMessage();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        const imageInput = document.getElementById('imageInput');
        const uploadArea = document.getElementById('uploadArea');
        const saveBtn = document.getElementById('saveBtn');
        const regenerateBtn = document.getElementById('regenerateBtn');
        
        // 文件选择事件
        imageInput.addEventListener('change', (e) => {
            if (e.target.files && e.target.files[0]) {
                this.handleImageUpload(e.target.files[0]);
            }
        });
        
        // 拖拽上传事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files && files[0] && files[0].type.startsWith('image/')) {
                this.handleImageUpload(files[0]);
            }
        });
        
        // 保存按钮事件
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveResultImage();
            });
        }
        
        // 重新生成按钮事件
        if (regenerateBtn) {
            regenerateBtn.addEventListener('click', () => {
                if (this.selectedRoom) {
                    this.generateRoomDesign(this.selectedRoom);
                }
            });
        }
    }
    
    // 设置画布
    setupCanvases() {
        this.floorplanCanvas = document.getElementById('floorplanCanvas');
        this.resultCanvas = document.getElementById('resultCanvas');
        
        if (this.floorplanCanvas) {
            this.floorplanCtx = this.floorplanCanvas.getContext('2d');
            
            // 添加画布点击事件
            this.floorplanCanvas.addEventListener('click', (e) => {
                this.handleCanvasClick(e);
            });
        }
        
        if (this.resultCanvas) {
            this.resultCtx = this.resultCanvas.getContext('2d');
        }
    }
    
    // 显示欢迎信息
    showWelcomeMessage() {
        console.log('✨ 欢迎使用房型智能装修设计系统！');
        console.log('📋 功能说明：');
        console.log('   1. 上传房型图片');
        console.log('   2. AI自动识别房间');
        console.log('   3. 点击房间生成装修效果');
        console.log('💡 完全免费，无需注册！');
    }
    
    // 处理图片上传
    async handleImageUpload(file) {
        if (this.isProcessing) {
            this.showMessage('正在处理中，请稍候...', 'warning');
            return;
        }
        
        // 验证文件类型
        if (!file.type.startsWith('image/')) {
            this.showMessage('请选择有效的图片文件！', 'error');
            return;
        }
        
        // 验证文件大小 (限制10MB)
        if (file.size > 10 * 1024 * 1024) {
            this.showMessage('图片文件过大，请选择小于10MB的图片！', 'error');
            return;
        }
        
        try {
            this.isProcessing = true;
            this.showProcessingStatus('正在加载图片...', 10);
            
            // 读取图片文件
            const imageDataUrl = await this.readFileAsDataURL(file);
            const image = await this.loadImage(imageDataUrl);
            
            this.currentImage = image;
            this.showProcessingStatus('图片加载完成，开始分析房型...', 30);
            
            // 开始房间识别
            await this.processFloorplan(image);
            
        } catch (error) {
            console.error('图片处理失败:', error);
            this.showMessage('图片处理失败，请重试！', 'error');
            this.isProcessing = false;
            this.hideProcessingStatus();
        }
    }
    
    // 读取文件为DataURL
    readFileAsDataURL(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }
    
    // 加载图片
    loadImage(src) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = reject;
            img.src = src;
        });
    }
    
    // 处理房型图
    async processFloorplan(image) {
        try {
            this.showProcessingStatus('正在识别房间结构...', 50);
            
            // 暂时使用模拟数据，后续会替换为真实的AI识别
            await this.simulateRoomDetection(image);
            
            this.showProcessingStatus('房间识别完成！', 100);
            
            // 显示结果
            setTimeout(() => {
                this.displayFloorplanResult();
                this.hideProcessingStatus();
                this.showMainWorkspace();
                this.isProcessing = false;
            }, 1000);
            
        } catch (error) {
            console.error('房型处理失败:', error);
            throw error;
        }
    }
    
    // 模拟房间检测（临时实现）
    async simulateRoomDetection(image) {
        // 模拟AI处理时间
        await this.delay(2000);
        
        // 生成模拟的房间数据
        this.detectedRooms = [
            {
                id: 1,
                type: 'living_room',
                name: '客厅',
                color: '#4A90E2',
                coordinates: [
                    [50, 50], [200, 50], [200, 150], [50, 150]
                ],
                center: [125, 100],
                area: 25
            },
            {
                id: 2,
                type: 'bedroom',
                name: '卧室',
                color: '#7ED321',
                coordinates: [
                    [220, 50], [350, 50], [350, 150], [220, 150]
                ],
                center: [285, 100],
                area: 18
            },
            {
                id: 3,
                type: 'kitchen',
                name: '厨房',
                color: '#F5A623',
                coordinates: [
                    [50, 170], [150, 170], [150, 250], [50, 250]
                ],
                center: [100, 210],
                area: 12
            },
            {
                id: 4,
                type: 'bathroom',
                name: '卫生间',
                color: '#BD10E0',
                coordinates: [
                    [170, 170], [220, 170], [220, 220], [170, 220]
                ],
                center: [195, 195],
                area: 6
            }
        ];
        
        console.log('🏠 检测到房间:', this.detectedRooms.length, '个');
    }
    
    // 延迟函数
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // 显示处理状态
    showProcessingStatus(message, progress) {
        const statusSection = document.getElementById('statusSection');
        const statusTitle = document.getElementById('statusTitle');
        const statusDescription = document.getElementById('statusDescription');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        
        if (statusSection) {
            statusSection.style.display = 'block';
        }
        
        if (statusTitle) {
            statusTitle.textContent = message;
        }
        
        if (statusDescription) {
            statusDescription.textContent = '请耐心等待，AI正在努力工作中...';
        }
        
        if (progressFill) {
            progressFill.style.width = progress + '%';
        }
        
        if (progressText) {
            progressText.textContent = progress + '%';
        }
    }
    
    // 隐藏处理状态
    hideProcessingStatus() {
        const statusSection = document.getElementById('statusSection');
        if (statusSection) {
            statusSection.style.display = 'none';
        }
    }
    
    // 显示主工作区
    showMainWorkspace() {
        const mainWorkspace = document.getElementById('mainWorkspace');
        if (mainWorkspace) {
            mainWorkspace.style.display = 'block';
            mainWorkspace.scrollIntoView({ behavior: 'smooth' });
        }
    }
    
    // 显示房型识别结果
    displayFloorplanResult() {
        if (!this.currentImage || !this.detectedRooms.length) {
            return;
        }
        
        // 设置画布尺寸
        const maxWidth = 400;
        const maxHeight = 300;
        const scale = Math.min(maxWidth / this.currentImage.width, maxHeight / this.currentImage.height);
        
        this.floorplanCanvas.width = this.currentImage.width * scale;
        this.floorplanCanvas.height = this.currentImage.height * scale;
        
        // 绘制原始图片
        this.floorplanCtx.drawImage(
            this.currentImage, 
            0, 0, 
            this.floorplanCanvas.width, 
            this.floorplanCanvas.height
        );
        
        // 绘制房间标注
        this.drawRoomAnnotations(scale);
        
        // 生成房间图例
        this.generateRoomLegend();
        
        console.log('✅ 房型图显示完成');
    }
    
    // 绘制房间标注
    drawRoomAnnotations(scale) {
        this.floorplanCtx.globalAlpha = 0.6;
        
        this.detectedRooms.forEach(room => {
            // 绘制房间区域
            this.floorplanCtx.fillStyle = room.color;
            this.floorplanCtx.beginPath();
            
            room.coordinates.forEach((point, index) => {
                const x = point[0] * scale;
                const y = point[1] * scale;
                
                if (index === 0) {
                    this.floorplanCtx.moveTo(x, y);
                } else {
                    this.floorplanCtx.lineTo(x, y);
                }
            });
            
            this.floorplanCtx.closePath();
            this.floorplanCtx.fill();
            
            // 绘制房间边框
            this.floorplanCtx.globalAlpha = 1;
            this.floorplanCtx.strokeStyle = room.color;
            this.floorplanCtx.lineWidth = 2;
            this.floorplanCtx.stroke();
            
            // 绘制房间标签
            this.floorplanCtx.fillStyle = '#fff';
            this.floorplanCtx.font = 'bold 14px Arial';
            this.floorplanCtx.textAlign = 'center';
            this.floorplanCtx.fillText(
                room.name,
                room.center[0] * scale,
                room.center[1] * scale
            );
            
            this.floorplanCtx.globalAlpha = 0.6;
        });
        
        this.floorplanCtx.globalAlpha = 1;
    }
    
    // 生成房间图例
    generateRoomLegend() {
        const legendContainer = document.getElementById('roomLegend');
        if (!legendContainer) return;
        
        legendContainer.innerHTML = '';
        
        this.detectedRooms.forEach(room => {
            const legendItem = document.createElement('div');
            legendItem.className = 'legend-item';
            
            legendItem.innerHTML = `
                <div class="legend-color" style="background-color: ${room.color}"></div>
                <span>${room.name} (${room.area}㎡)</span>
            `;
            
            legendContainer.appendChild(legendItem);
        });
    }
    
    // 处理画布点击事件
    handleCanvasClick(event) {
        if (!this.detectedRooms.length || this.isProcessing) {
            return;
        }
        
        const rect = this.floorplanCanvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        // 检查点击的是哪个房间
        const clickedRoom = this.findClickedRoom(x, y);
        
        if (clickedRoom) {
            this.selectRoom(clickedRoom);
        }
    }
    
    // 查找被点击的房间
    findClickedRoom(x, y) {
        const scale = this.floorplanCanvas.width / this.currentImage.width;
        
        for (let room of this.detectedRooms) {
            if (this.isPointInPolygon(x / scale, y / scale, room.coordinates)) {
                return room;
            }
        }
        
        return null;
    }
    
    // 检查点是否在多边形内
    isPointInPolygon(x, y, polygon) {
        let inside = false;
        
        for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
            const xi = polygon[i][0], yi = polygon[i][1];
            const xj = polygon[j][0], yj = polygon[j][1];
            
            if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
                inside = !inside;
            }
        }
        
        return inside;
    }
    
    // 选择房间
    async selectRoom(room) {
        this.selectedRoom = room;
        
        // 更新选中房间信息
        const selectedRoomInfo = document.getElementById('selectedRoomInfo');
        if (selectedRoomInfo) {
            selectedRoomInfo.textContent = `正在生成 ${room.name} 的装修效果图...`;
        }
        
        console.log('🎯 选中房间:', room.name);
        
        // 生成装修效果图
        await this.generateRoomDesign(room);
    }
    
    // 生成房间装修设计
    async generateRoomDesign(room) {
        try {
            this.isProcessing = true;
            
            // 隐藏占位符
            const placeholder = document.getElementById('resultPlaceholder');
            if (placeholder) {
                placeholder.style.display = 'none';
            }
            
            // 更新状态信息
            const selectedRoomInfo = document.getElementById('selectedRoomInfo');
            if (selectedRoomInfo) {
                selectedRoomInfo.textContent = `正在为 ${room.name} 生成装修效果图...`;
            }
            
            // 暂时使用模拟生成，后续替换为真实AI
            await this.simulateDesignGeneration(room);
            
            // 显示操作按钮
            const resultActions = document.getElementById('resultActions');
            if (resultActions) {
                resultActions.style.display = 'flex';
            }
            
            // 更新状态信息
            if (selectedRoomInfo) {
                selectedRoomInfo.textContent = `${room.name} 装修效果图`;
            }
            
            this.isProcessing = false;
            
        } catch (error) {
            console.error('装修效果图生成失败:', error);
            this.showMessage('装修效果图生成失败，请重试！', 'error');
            this.isProcessing = false;
        }
    }
    
    // AI装修效果图生成
    async simulateDesignGeneration(room) {
        try {
            // 显示生成进度
            this.showGenerationProgress('正在生成AI装修效果图...');

            // 生成装修提示词
            const prompt = this.generateDesignPrompt(room);
            console.log('🎨 生成提示词:', prompt);

            // 调用AI图像生成API
            const imageUrl = await this.generateAIDesignImage(prompt);

            if (imageUrl) {
                // 显示AI生成的图片
                await this.displayAIGeneratedImage(imageUrl, room);
            } else {
                // 如果AI生成失败，使用备用方案
                console.log('⚠️ AI生成失败，使用备用方案');
                await this.generateFallbackDesign(room);
            }

            console.log('🎨 装修效果图生成完成');
        } catch (error) {
            console.error('❌ 装修效果图生成失败:', error);
            // 使用备用方案
            await this.generateFallbackDesign(room);
        }
    }

    // 生成装修设计提示词
    generateDesignPrompt(room) {
        const roomPrompts = {
            'living_room': 'luxury modern living room interior, elegant sofa set, marble coffee table, 75 inch smart TV on wall, ambient lighting, minimalist contemporary design, hardwood floors, large windows, plants, warm atmosphere, professional interior photography, ultra realistic, 4k quality',
            'bedroom': 'beautiful modern bedroom interior, king size bed with white bedding, wooden nightstands, built-in wardrobe, soft warm lighting, cozy atmosphere, carpet, curtains, minimalist design, professional interior photography, ultra realistic, 4k quality',
            'kitchen': 'stunning modern kitchen interior, white cabinets, quartz countertops, stainless steel appliances, kitchen island, pendant lights, backsplash tiles, hardwood floors, clean contemporary design, professional interior photography, ultra realistic, 4k quality',
            'bathroom': 'luxurious modern bathroom interior, freestanding bathtub, glass shower, double vanity, marble tiles, mirror with lighting, contemporary fixtures, spa-like atmosphere, professional interior photography, ultra realistic, 4k quality',
            'balcony': 'beautiful modern balcony design, outdoor furniture, wooden decking, plants and flowers, city skyline view, comfortable seating area, outdoor lighting, relaxing atmosphere, professional photography, ultra realistic, 4k quality'
        };

        const basePrompt = roomPrompts[room.type] || `modern ${room.name} interior design, furniture, contemporary style, professional interior photography, ultra realistic, 4k quality`;

        // 添加通用的高质量提示词
        const qualityPrompts = [
            'professional interior design',
            'architectural photography',
            'perfect lighting',
            'high resolution',
            'detailed textures',
            'realistic materials',
            'depth of field',
            'award winning design'
        ];

        return `${basePrompt}, ${qualityPrompts.join(', ')}`;
    }

    // 调用AI图像生成API
    async generateAIDesignImage(prompt) {
        try {
            // 显示API调用进度
            this.showGenerationProgress('正在调用AI图像生成服务...');

            // 尝试使用免费的AI图像生成服务
            const aiResult = await this.tryMultipleAIServices(prompt);
            if (aiResult) {
                return aiResult;
            }

            console.log('🔄 所有AI服务不可用，使用高质量本地生成...');
            return null; // 触发备用方案
        } catch (error) {
            console.error('❌ AI图像生成失败:', error);
            return null;
        }
    }

    // 尝试多个AI服务
    async tryMultipleAIServices(prompt) {
        // 方案1: 使用免费的Pollinations AI
        try {
            this.showGenerationProgress('尝试Pollinations AI服务...');
            const pollinationsResult = await this.callPollinationsAI(prompt);
            if (pollinationsResult) return pollinationsResult;
        } catch (error) {
            console.log('Pollinations AI不可用:', error.message);
        }

        // 方案2: 使用免费的Craiyon (原DALL-E mini)
        try {
            this.showGenerationProgress('尝试Craiyon AI服务...');
            const craiyonResult = await this.callCraiyonAI(prompt);
            if (craiyonResult) return craiyonResult;
        } catch (error) {
            console.log('Craiyon AI不可用:', error.message);
        }

        // 方案3: 使用免费的Stable Diffusion在线服务
        try {
            this.showGenerationProgress('尝试在线Stable Diffusion...');
            const stableDiffusionResult = await this.callOnlineStableDiffusion(prompt);
            if (stableDiffusionResult) return stableDiffusionResult;
        } catch (error) {
            console.log('在线Stable Diffusion不可用:', error.message);
        }

        return null;
    }

    // Pollinations AI - 完全免费的AI图像生成
    async callPollinationsAI(prompt) {
        try {
            // Pollinations提供免费的AI图像生成，无需API密钥
            const encodedPrompt = encodeURIComponent(prompt);
            const imageUrl = `https://image.pollinations.ai/prompt/${encodedPrompt}?width=512&height=512&seed=${Math.floor(Math.random() * 1000000)}`;

            // 验证图片是否可以加载
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = () => resolve(imageUrl);
                img.onerror = () => resolve(null);
                img.src = imageUrl;

                // 设置超时
                setTimeout(() => resolve(null), 10000);
            });
        } catch (error) {
            console.error('Pollinations AI调用失败:', error);
            return null;
        }
    }

    // Craiyon AI - 免费的DALL-E替代品
    async callCraiyonAI(prompt) {
        try {
            const response = await fetch('https://backend.craiyon.com/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt: prompt,
                    version: "3",
                    token: null,
                    is_premium: false,
                    negative_prompt: "",
                    model: "art"
                })
            });

            if (response.ok) {
                const result = await response.json();
                if (result.images && result.images.length > 0) {
                    // Craiyon返回base64编码的图片
                    return `data:image/jpeg;base64,${result.images[0]}`;
                }
            }
            return null;
        } catch (error) {
            console.error('Craiyon AI调用失败:', error);
            return null;
        }
    }

    // 在线Stable Diffusion服务
    async callOnlineStableDiffusion(prompt) {
        try {
            // 使用免费的Stable Diffusion在线服务
            const response = await fetch('https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    text_prompts: [
                        {
                            text: prompt,
                            weight: 1
                        }
                    ],
                    cfg_scale: 7,
                    height: 512,
                    width: 512,
                    samples: 1,
                    steps: 20,
                })
            });

            if (response.ok) {
                const result = await response.json();
                if (result.artifacts && result.artifacts.length > 0) {
                    return `data:image/png;base64,${result.artifacts[0].base64}`;
                }
            }
            return null;
        } catch (error) {
            console.error('在线Stable Diffusion调用失败:', error);
            return null;
        }
    }

    // 显示AI生成的图片
    async displayAIGeneratedImage(imageUrl, room) {
        return new Promise((resolve) => {
            this.showGenerationProgress('正在加载AI生成的效果图...');

            const img = new Image();
            img.onload = () => {
                try {
                    // 设置画布尺寸
                    this.resultCanvas.width = 400;
                    this.resultCanvas.height = 300;

                    // 清空画布
                    this.resultCtx.clearRect(0, 0, 400, 300);

                    // 绘制AI生成的图片，保持比例
                    const aspectRatio = img.width / img.height;
                    let drawWidth = 400;
                    let drawHeight = 300;
                    let offsetX = 0;
                    let offsetY = 0;

                    if (aspectRatio > 400/300) {
                        // 图片更宽，以宽度为准
                        drawHeight = 400 / aspectRatio;
                        offsetY = (300 - drawHeight) / 2;
                    } else {
                        // 图片更高，以高度为准
                        drawWidth = 300 * aspectRatio;
                        offsetX = (400 - drawWidth) / 2;
                    }

                    // 绘制背景
                    this.resultCtx.fillStyle = '#f8f9fa';
                    this.resultCtx.fillRect(0, 0, 400, 300);

                    // 绘制AI生成的图片
                    this.resultCtx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);

                    // 添加AI标识
                    this.addAIWatermark(room, true);

                    console.log('✅ AI装修效果图加载成功');
                    resolve();
                } catch (error) {
                    console.error('❌ 图片绘制失败:', error);
                    this.generateFallbackDesign(room).then(resolve);
                }
            };

            img.onerror = () => {
                console.error('❌ AI图片加载失败，使用备用方案');
                this.generateFallbackDesign(room).then(resolve);
            };

            // 设置跨域属性
            img.crossOrigin = 'anonymous';
            img.src = imageUrl;

            // 设置超时
            setTimeout(() => {
                if (!img.complete) {
                    console.log('⏰ AI图片加载超时，使用备用方案');
                    this.generateFallbackDesign(room).then(resolve);
                }
            }, 15000);
        });
    }

    // 添加AI水印
    addAIWatermark(room, isRealAI = false) {
        const ctx = this.resultCtx;

        // 添加渐变背景标题栏
        const gradient = ctx.createLinearGradient(0, 260, 0, 300);
        gradient.addColorStop(0, 'rgba(0, 0, 0, 0.8)');
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0.9)');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 260, 400, 40);

        // 添加AI图标和标识
        if (isRealAI) {
            ctx.fillStyle = '#00D4AA';
            ctx.font = '16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('🤖', 10, 280);

            ctx.fillStyle = '#FFFFFF';
            ctx.font = 'bold 14px Arial';
            ctx.fillText(`AI生成 - ${room.name}装修效果图`, 35, 280);

            ctx.fillStyle = '#00D4AA';
            ctx.font = '10px Arial';
            ctx.textAlign = 'right';
            ctx.fillText('真实AI渲染', 390, 280);
        } else {
            ctx.fillStyle = '#667eea';
            ctx.font = '16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('🎨', 10, 280);

            ctx.fillStyle = '#FFFFFF';
            ctx.font = 'bold 14px Arial';
            ctx.fillText(`高质量 - ${room.name}装修效果图`, 35, 280);

            ctx.fillStyle = '#FFD700';
            ctx.font = '10px Arial';
            ctx.textAlign = 'right';
            ctx.fillText('专业渲染', 390, 280);
        }
    }

    // 显示生成进度
    showGenerationProgress(message) {
        // 在画布上显示进度信息
        this.resultCanvas.width = 400;
        this.resultCanvas.height = 300;

        // 绘制进度背景
        const gradient = this.resultCtx.createLinearGradient(0, 0, 400, 300);
        gradient.addColorStop(0, '#667eea');
        gradient.addColorStop(1, '#764ba2');
        this.resultCtx.fillStyle = gradient;
        this.resultCtx.fillRect(0, 0, 400, 300);

        // 绘制加载动画
        const time = Date.now() * 0.005;
        this.resultCtx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        for (let i = 0; i < 3; i++) {
            const radius = 15 + Math.sin(time + i * 0.8) * 8;
            this.resultCtx.beginPath();
            this.resultCtx.arc(150 + i * 50, 150, radius, 0, Math.PI * 2);
            this.resultCtx.fill();
        }

        // 绘制旋转的AI图标
        this.resultCtx.save();
        this.resultCtx.translate(200, 120);
        this.resultCtx.rotate(time);
        this.resultCtx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        this.resultCtx.font = '30px Arial';
        this.resultCtx.textAlign = 'center';
        this.resultCtx.fillText('🤖', 0, 10);
        this.resultCtx.restore();

        // 绘制进度文字
        this.resultCtx.fillStyle = '#FFFFFF';
        this.resultCtx.font = 'bold 20px Arial';
        this.resultCtx.textAlign = 'center';
        this.resultCtx.fillText('AI装修大师', 200, 90);

        this.resultCtx.font = '14px Arial';
        this.resultCtx.fillText(message, 200, 200);

        // 根据消息类型显示不同的提示
        if (message.includes('AI')) {
            this.resultCtx.fillStyle = '#00D4AA';
            this.resultCtx.fillText('🎯 正在调用真实AI服务生成专业效果图', 200, 220);
            this.resultCtx.fillStyle = '#FFFFFF';
            this.resultCtx.fillText('预计需要10-30秒，请耐心等待...', 200, 240);
        } else {
            this.resultCtx.fillText('正在为您打造专属设计方案...', 200, 220);
        }

        // 添加进度条
        const progressWidth = 200;
        const progressHeight = 4;
        const progressX = (400 - progressWidth) / 2;
        const progressY = 260;

        // 进度条背景
        this.resultCtx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        this.resultCtx.fillRect(progressX, progressY, progressWidth, progressHeight);

        // 进度条动画
        const progress = (Math.sin(time * 2) + 1) / 2;
        this.resultCtx.fillStyle = '#00D4AA';
        this.resultCtx.fillRect(progressX, progressY, progressWidth * progress, progressHeight);
    }

    // 备用方案：使用免费AI服务或本地生成
    async generateFallbackDesign(room) {
        try {
            // 尝试使用其他免费AI服务
            const freeAIResult = await this.tryFreeAIServices(room);
            if (freeAIResult) {
                await this.displayAIGeneratedImage(freeAIResult, room);
                return;
            }

            // 如果所有AI服务都失败，使用高质量本地生成
            await this.generateHighQualityLocalDesign(room);
        } catch (error) {
            console.error('❌ 备用方案失败:', error);
            // 最后的备用方案
            await this.generateBasicLocalDesign(room);
        }
    }

    // 尝试免费AI服务
    async tryFreeAIServices(room) {
        const prompt = this.generateDesignPrompt(room);

        // 方案1: 使用DeepAI免费API (每月有免费额度)
        try {
            this.showGenerationProgress('尝试DeepAI免费服务...');
            const deepAIResult = await this.callDeepAI(prompt);
            if (deepAIResult) return deepAIResult;
        } catch (error) {
            console.log('DeepAI服务不可用:', error.message);
        }

        // 方案2: 使用本地Stable Diffusion (如果用户有安装)
        try {
            this.showGenerationProgress('尝试本地AI服务...');
            const localResult = await this.callLocalStableDiffusion(prompt);
            if (localResult) return localResult;
        } catch (error) {
            console.log('本地AI服务不可用:', error.message);
        }

        return null;
    }

    // DeepAI免费API调用
    async callDeepAI(prompt) {
        try {
            const response = await fetch('https://api.deepai.org/api/text2img', {
                method: 'POST',
                headers: {
                    'Api-Key': 'quickstart-QUdJIGlzIGNvbWluZy4uLi4K', // 免费试用key
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    text: prompt,
                    grid_size: "1",
                    width: 512,
                    height: 512
                })
            });

            if (response.ok) {
                const result = await response.json();
                return result.output_url;
            }
        } catch (error) {
            console.error('DeepAI调用失败:', error);
        }
        return null;
    }

    // 本地Stable Diffusion调用
    async callLocalStableDiffusion(prompt) {
        try {
            // 尝试连接本地Stable Diffusion WebUI (默认端口7860)
            const response = await fetch('http://127.0.0.1:7860/api/v1/txt2img', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt: prompt,
                    steps: 20,
                    width: 512,
                    height: 512,
                    cfg_scale: 7
                })
            });

            if (response.ok) {
                const result = await response.json();
                return `data:image/png;base64,${result.images[0]}`;
            }
        } catch (error) {
            console.error('本地SD调用失败:', error);
        }
        return null;
    }

    // 高质量本地生成（使用Canvas绘制精美效果图）
    async generateHighQualityLocalDesign(room) {
        this.showGenerationProgress('生成高质量本地效果图...');
        await this.delay(1000);

        // 设置画布尺寸
        this.resultCanvas.width = 400;
        this.resultCanvas.height = 300;

        // 根据房间类型生成高质量效果图
        switch (room.type) {
            case 'living_room':
                await this.drawHighQualityLivingRoom();
                break;
            case 'bedroom':
                await this.drawHighQualityBedroom();
                break;
            case 'kitchen':
                await this.drawHighQualityKitchen();
                break;
            case 'bathroom':
                await this.drawHighQualityBathroom();
                break;
            case 'balcony':
                await this.drawHighQualityBalcony();
                break;
            default:
                await this.drawHighQualityDefaultRoom(room);
        }

        // 添加AI风格标识
        this.addAIStyleWatermark(room);
    }

    // 添加AI风格水印
    addAIStyleWatermark(room) {
        const ctx = this.resultCtx;

        // 添加渐变背景标题栏
        const gradient = ctx.createLinearGradient(0, 260, 0, 300);
        gradient.addColorStop(0, 'rgba(0, 0, 0, 0.8)');
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0.9)');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 260, 400, 40);

        // 添加AI图标
        ctx.fillStyle = '#00D4AA';
        ctx.font = '16px Arial';
        ctx.textAlign = 'left';
        ctx.fillText('🤖', 10, 280);

        // 添加标题
        ctx.fillStyle = '#FFFFFF';
        ctx.font = 'bold 14px Arial';
        ctx.fillText(`AI装修大师 - ${room.name}设计方案`, 35, 280);

        // 添加质量标识
        ctx.fillStyle = '#FFD700';
        ctx.font = '10px Arial';
        ctx.textAlign = 'right';
        ctx.fillText('高质量渲染', 390, 280);
    }

    // 高质量客厅设计
    async drawHighQualityLivingRoom() {
        const ctx = this.resultCtx;

        // 创建3D透视背景
        const bgGradient = ctx.createLinearGradient(0, 0, 400, 300);
        bgGradient.addColorStop(0, '#f8f9fa');
        bgGradient.addColorStop(0.3, '#e9ecef');
        bgGradient.addColorStop(1, '#dee2e6');
        ctx.fillStyle = bgGradient;
        ctx.fillRect(0, 0, 400, 300);

        // 绘制地板（木纹效果）
        this.drawWoodFloor(ctx);

        // 绘制墙面（带纹理）
        this.drawTexturedWall(ctx);

        // 绘制现代沙发
        this.drawModernSofa(ctx, 60, 160, 140, 70);

        // 绘制玻璃茶几
        this.drawGlassCoffeeTable(ctx, 220, 180, 100, 50);

        // 绘制电视墙
        this.drawTVWall(ctx, 320, 100, 70, 80);

        // 绘制落地窗
        this.drawFloorWindow(ctx, 20, 40, 80, 120);

        // 绘制装饰画
        this.drawArtwork(ctx, 150, 50, 60, 40);

        // 绘制绿植
        this.drawPlant(ctx, 350, 140, 30, 60);

        // 添加光影效果
        this.addLightingEffects(ctx);
    }

    // 高质量卧室设计
    async drawHighQualityBedroom() {
        const ctx = this.resultCtx;

        // 温馨背景
        const bgGradient = ctx.createLinearGradient(0, 0, 400, 300);
        bgGradient.addColorStop(0, '#fff8f0');
        bgGradient.addColorStop(1, '#f5e6d3');
        ctx.fillStyle = bgGradient;
        ctx.fillRect(0, 0, 400, 300);

        // 绘制地板
        this.drawWoodFloor(ctx);

        // 绘制大床
        this.drawLuxuryBed(ctx, 100, 150, 180, 90);

        // 绘制床头柜
        this.drawNightstand(ctx, 50, 170, 40, 50);
        this.drawNightstand(ctx, 290, 170, 40, 50);

        // 绘制衣柜
        this.drawWardrobe(ctx, 320, 80, 70, 140);

        // 绘制窗户和窗帘
        this.drawWindowWithCurtains(ctx, 20, 40, 80, 100);

        // 绘制台灯
        this.drawTableLamp(ctx, 60, 160, 20, 25);

        // 添加温馨光效
        this.addWarmLighting(ctx);
    }

    // 高质量厨房设计
    async drawHighQualityKitchen() {
        const ctx = this.resultCtx;

        // 现代厨房背景
        const bgGradient = ctx.createLinearGradient(0, 0, 400, 300);
        bgGradient.addColorStop(0, '#ffffff');
        bgGradient.addColorStop(1, '#f8f9fa');
        ctx.fillStyle = bgGradient;
        ctx.fillRect(0, 0, 400, 300);

        // 绘制瓷砖地板
        this.drawTileFloor(ctx);

        // 绘制现代橱柜
        this.drawModernCabinets(ctx);

        // 绘制岛台
        this.drawKitchenIsland(ctx, 150, 160, 120, 60);

        // 绘制高端电器
        this.drawKitchenAppliances(ctx);

        // 添加厨房照明
        this.addKitchenLighting(ctx);
    }

    // 高质量卫生间设计
    async drawHighQualityBathroom() {
        const ctx = this.resultCtx;

        // 现代卫生间背景
        const bgGradient = ctx.createLinearGradient(0, 0, 400, 300);
        bgGradient.addColorStop(0, '#f0f8ff');
        bgGradient.addColorStop(1, '#e6f3ff');
        ctx.fillStyle = bgGradient;
        ctx.fillRect(0, 0, 400, 300);

        // 绘制大理石地板
        this.drawMarbleFloor(ctx);

        // 绘制现代浴缸
        this.drawModernBathtub(ctx, 50, 140, 140, 80);

        // 绘制玻璃淋浴房
        this.drawGlassShower(ctx, 220, 120, 80, 100);

        // 绘制现代洗手台
        this.drawModernVanity(ctx, 320, 140, 70, 60);

        // 添加卫生间照明
        this.addBathroomLighting(ctx);
    }

    // 高质量阳台设计
    async drawHighQualityBalcony() {
        const ctx = this.resultCtx;

        // 天空背景
        const skyGradient = ctx.createLinearGradient(0, 0, 0, 200);
        skyGradient.addColorStop(0, '#87ceeb');
        skyGradient.addColorStop(0.7, '#b0e0e6');
        skyGradient.addColorStop(1, '#f0f8ff');
        ctx.fillStyle = skyGradient;
        ctx.fillRect(0, 0, 400, 200);

        // 绘制防腐木地板
        this.drawWoodDeck(ctx);

        // 绘制现代栏杆
        this.drawModernRailing(ctx);

        // 绘制户外家具
        this.drawOutdoorFurniture(ctx);

        // 绘制绿植装饰
        this.drawBalconyPlants(ctx);

        // 添加自然光效
        this.addNaturalLighting(ctx);
    }

    // 高质量默认房间设计
    async drawHighQualityDefaultRoom(room) {
        const ctx = this.resultCtx;

        // 通用背景
        const bgGradient = ctx.createLinearGradient(0, 0, 400, 300);
        bgGradient.addColorStop(0, '#ffffff');
        bgGradient.addColorStop(1, '#f5f5f5');
        ctx.fillStyle = bgGradient;
        ctx.fillRect(0, 0, 400, 300);

        // 绘制地板
        this.drawWoodFloor(ctx);

        // 绘制基础家具
        this.drawBasicFurniture(ctx, room);

        // 添加基础照明
        this.addBasicLighting(ctx);
    }

    // 基础绘制方法：备用方案
    async generateBasicLocalDesign(room) {
        // 设置画布尺寸
        this.resultCanvas.width = 400;
        this.resultCanvas.height = 300;

        // 根据房间类型生成基础效果图
        switch (room.type) {
            case 'living_room':
                await this.drawLivingRoomDesign();
                break;
            case 'bedroom':
                await this.drawBedroomDesign();
                break;
            case 'kitchen':
                await this.drawKitchenDesign();
                break;
            case 'bathroom':
                await this.drawBathroomDesign();
                break;
            case 'balcony':
                await this.drawBalconyDesign();
                break;
            default:
                await this.drawDefaultRoomDesign(room);
        }

        // 添加基础AI标识
        this.addBasicAIWatermark(room);
    }

    // 添加基础AI水印
    addBasicAIWatermark(room) {
        const ctx = this.resultCtx;

        // 添加半透明背景
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(0, 260, 400, 40);

        // 添加标题
        ctx.fillStyle = '#FFFFFF';
        ctx.font = 'bold 14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`🏠 ${room.name}装修设计方案`, 200, 280);
    }

    // ===== 绘制工具方法 =====

    // 绘制木纹地板
    drawWoodFloor(ctx) {
        const floorGradient = ctx.createLinearGradient(0, 200, 0, 300);
        floorGradient.addColorStop(0, '#d4a574');
        floorGradient.addColorStop(0.5, '#c19660');
        floorGradient.addColorStop(1, '#a67c52');
        ctx.fillStyle = floorGradient;
        ctx.fillRect(0, 200, 400, 100);

        // 添加木纹纹理
        ctx.strokeStyle = 'rgba(139, 69, 19, 0.3)';
        ctx.lineWidth = 1;
        for (let i = 0; i < 400; i += 60) {
            ctx.beginPath();
            ctx.moveTo(i, 200);
            ctx.lineTo(i, 300);
            ctx.stroke();
        }

        // 添加木板接缝
        ctx.strokeStyle = 'rgba(139, 69, 19, 0.2)';
        for (let i = 220; i < 300; i += 20) {
            ctx.beginPath();
            ctx.moveTo(0, i);
            ctx.lineTo(400, i);
            ctx.stroke();
        }
    }

    // 绘制纹理墙面
    drawTexturedWall(ctx) {
        const wallGradient = ctx.createLinearGradient(0, 0, 0, 200);
        wallGradient.addColorStop(0, '#ffffff');
        wallGradient.addColorStop(0.7, '#f8f9fa');
        wallGradient.addColorStop(1, '#e9ecef');
        ctx.fillStyle = wallGradient;
        ctx.fillRect(0, 0, 400, 200);

        // 添加墙面纹理
        ctx.fillStyle = 'rgba(0, 0, 0, 0.02)';
        for (let i = 0; i < 100; i++) {
            const x = Math.random() * 400;
            const y = Math.random() * 200;
            ctx.fillRect(x, y, 1, 1);
        }
    }

    // 绘制现代沙发
    drawModernSofa(ctx, x, y, width, height) {
        // 沙发阴影
        ctx.fillStyle = 'rgba(0, 0, 0, 0.2)';
        ctx.fillRect(x + 5, y + height, width, 8);

        // 沙发主体
        const sofaGradient = ctx.createLinearGradient(x, y, x, y + height);
        sofaGradient.addColorStop(0, '#4a5568');
        sofaGradient.addColorStop(0.5, '#2d3748');
        sofaGradient.addColorStop(1, '#1a202c');
        ctx.fillStyle = sofaGradient;
        ctx.fillRect(x, y, width, height);

        // 沙发靠背
        const backGradient = ctx.createLinearGradient(x, y - 20, x, y + 5);
        backGradient.addColorStop(0, '#4a5568');
        backGradient.addColorStop(1, '#2d3748');
        ctx.fillStyle = backGradient;
        ctx.fillRect(x, y - 20, width, 25);

        // 沙发扶手
        ctx.fillStyle = '#2d3748';
        ctx.fillRect(x - 10, y - 15, 15, height + 15);
        ctx.fillRect(x + width - 5, y - 15, 15, height + 15);

        // 沙发坐垫分割线
        ctx.strokeStyle = '#1a202c';
        ctx.lineWidth = 2;
        for (let i = 1; i < 3; i++) {
            const dividerX = x + (width / 3) * i;
            ctx.beginPath();
            ctx.moveTo(dividerX, y);
            ctx.lineTo(dividerX, y + height);
            ctx.stroke();
        }
    }

    // 绘制玻璃茶几
    drawGlassCoffeeTable(ctx, x, y, width, height) {
        // 茶几阴影
        ctx.fillStyle = 'rgba(0, 0, 0, 0.15)';
        ctx.fillRect(x + 3, y + height + 20, width, 5);

        // 玻璃台面
        const glassGradient = ctx.createLinearGradient(x, y, x + width, y + height);
        glassGradient.addColorStop(0, 'rgba(255, 255, 255, 0.9)');
        glassGradient.addColorStop(0.5, 'rgba(240, 248, 255, 0.8)');
        glassGradient.addColorStop(1, 'rgba(255, 255, 255, 0.7)');
        ctx.fillStyle = glassGradient;
        ctx.fillRect(x, y, width, height);

        // 玻璃边框
        ctx.strokeStyle = 'rgba(200, 200, 200, 0.8)';
        ctx.lineWidth = 2;
        ctx.strokeRect(x, y, width, height);

        // 金属桌腿
        const legGradient = ctx.createLinearGradient(0, 0, 0, 20);
        legGradient.addColorStop(0, '#e0e0e0');
        legGradient.addColorStop(1, '#c0c0c0');
        ctx.fillStyle = legGradient;
        ctx.fillRect(x + 15, y + height, 8, 20);
        ctx.fillRect(x + width - 23, y + height, 8, 20);
        ctx.fillRect(x + 15, y + height + 15, width - 30, 3);

        // 玻璃反光效果
        ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
        ctx.fillRect(x + 5, y + 5, width - 10, 8);
        ctx.fillRect(x + 5, y + 5, 15, height - 10);
    }

    // 绘制电视墙
    drawTVWall(ctx, x, y, width, height) {
        // 电视背景墙
        const wallGradient = ctx.createLinearGradient(x - 10, y - 10, x + width + 10, y + height + 10);
        wallGradient.addColorStop(0, '#34495e');
        wallGradient.addColorStop(0.5, '#2c3e50');
        wallGradient.addColorStop(1, '#1a252f');
        ctx.fillStyle = wallGradient;
        ctx.fillRect(x - 10, y - 10, width + 20, height + 20);

        // 电视屏幕
        ctx.fillStyle = '#000000';
        ctx.fillRect(x, y, width, height);

        // 电视边框
        const frameGradient = ctx.createLinearGradient(x, y, x + width, y + height);
        frameGradient.addColorStop(0, '#4a4a4a');
        frameGradient.addColorStop(1, '#2a2a2a');
        ctx.fillStyle = frameGradient;
        ctx.fillRect(x - 2, y - 2, width + 4, height + 4);
        ctx.fillStyle = '#000000';
        ctx.fillRect(x, y, width, height);

        // 屏幕反光
        ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
        ctx.fillRect(x + 5, y + 5, width - 10, height - 10);

        // 电视底座
        ctx.fillStyle = '#2c3e50';
        ctx.fillRect(x + width/4, y + height, width/2, 8);
    }

    // 绘制落地窗
    drawFloorWindow(ctx, x, y, width, height) {
        // 窗框
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(x - 5, y - 5, width + 10, height + 10);

        // 玻璃
        const glassGradient = ctx.createLinearGradient(x, y, x + width, y + height);
        glassGradient.addColorStop(0, 'rgba(135, 206, 235, 0.8)');
        glassGradient.addColorStop(0.5, 'rgba(173, 216, 230, 0.6)');
        glassGradient.addColorStop(1, 'rgba(240, 248, 255, 0.4)');
        ctx.fillStyle = glassGradient;
        ctx.fillRect(x, y, width, height);

        // 窗格
        ctx.strokeStyle = '#654321';
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(x + width/2, y);
        ctx.lineTo(x + width/2, y + height);
        ctx.moveTo(x, y + height/2);
        ctx.lineTo(x + width, y + height/2);
        ctx.stroke();

        // 窗帘
        ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
        ctx.fillRect(x + width - 15, y, 15, height);

        // 外景模拟
        ctx.fillStyle = 'rgba(34, 139, 34, 0.3)';
        for (let i = 0; i < 5; i++) {
            const treeX = x + Math.random() * width;
            const treeY = y + height/2 + Math.random() * height/2;
            ctx.fillRect(treeX, treeY, 3, 15);
        }
    }

    async drawLivingRoomDesign() {
        const ctx = this.resultCtx;

        // 绘制地板
        ctx.fillStyle = '#D2B48C';
        ctx.fillRect(0, 200, 400, 100);

        // 绘制墙面
        const wallGradient = ctx.createLinearGradient(0, 0, 0, 200);
        wallGradient.addColorStop(0, '#F5F5DC');
        wallGradient.addColorStop(1, '#E6E6FA');
        ctx.fillStyle = wallGradient;
        ctx.fillRect(0, 0, 400, 200);

        // 绘制沙发
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(50, 150, 120, 60);
        ctx.fillStyle = '#A0522D';
        ctx.fillRect(55, 145, 110, 15);

        // 绘制茶几
        ctx.fillStyle = '#654321';
        ctx.fillRect(200, 170, 80, 40);
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(205, 165, 70, 10);

        // 绘制电视
        ctx.fillStyle = '#000';
        ctx.fillRect(300, 120, 80, 50);
        ctx.fillStyle = '#333';
        ctx.fillRect(305, 125, 70, 40);

        // 绘制窗户
        ctx.strokeStyle = '#8B4513';
        ctx.lineWidth = 3;
        ctx.strokeRect(20, 30, 60, 80);
        ctx.fillStyle = '#87CEEB';
        ctx.fillRect(25, 35, 50, 70);

        // 绘制装饰画
        ctx.fillStyle = '#FFD700';
        ctx.fillRect(150, 40, 40, 30);
        ctx.strokeStyle = '#8B4513';
        ctx.lineWidth = 2;
        ctx.strokeRect(150, 40, 40, 30);

        // 添加文字说明
        ctx.fillStyle = '#333';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('现代简约客厅设计', 200, 280);
    }

    // 绘制卧室装修效果
    async drawBedroomDesign() {
        const ctx = this.resultCtx;

        // 绘制地板
        ctx.fillStyle = '#DEB887';
        ctx.fillRect(0, 200, 400, 100);

        // 绘制墙面
        const wallGradient = ctx.createLinearGradient(0, 0, 0, 200);
        wallGradient.addColorStop(0, '#FFF8DC');
        wallGradient.addColorStop(1, '#F0E68C');
        ctx.fillStyle = wallGradient;
        ctx.fillRect(0, 0, 400, 200);

        // 绘制床
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(80, 140, 160, 80);
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(85, 135, 150, 20);

        // 绘制床头柜
        ctx.fillStyle = '#654321';
        ctx.fillRect(30, 160, 40, 50);
        ctx.fillRect(330, 160, 40, 50);

        // 绘制衣柜
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(300, 80, 80, 120);
        ctx.strokeStyle = '#654321';
        ctx.lineWidth = 2;
        ctx.strokeRect(340, 90, 2, 100);

        // 绘制窗户
        ctx.strokeStyle = '#8B4513';
        ctx.lineWidth = 3;
        ctx.strokeRect(20, 30, 60, 80);
        ctx.fillStyle = '#87CEEB';
        ctx.fillRect(25, 35, 50, 70);

        // 绘制台灯
        ctx.fillStyle = '#FFD700';
        ctx.fillRect(45, 150, 10, 15);
        ctx.fillRect(40, 145, 20, 8);

        // 添加文字说明
        ctx.fillStyle = '#333';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('温馨舒适卧室设计', 200, 280);
    }

    // 绘制厨房装修效果
    async drawKitchenDesign() {
        const ctx = this.resultCtx;

        // 绘制地板
        ctx.fillStyle = '#C0C0C0';
        ctx.fillRect(0, 200, 400, 100);

        // 绘制墙面
        ctx.fillStyle = '#F5F5F5';
        ctx.fillRect(0, 0, 400, 200);

        // 绘制橱柜
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(20, 150, 360, 60);
        ctx.fillStyle = '#A0522D';
        ctx.fillRect(25, 145, 350, 15);

        // 绘制台面
        ctx.fillStyle = '#696969';
        ctx.fillRect(20, 140, 360, 15);

        // 绘制吊柜
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(50, 60, 300, 40);

        // 绘制冰箱
        ctx.fillStyle = '#C0C0C0';
        ctx.fillRect(320, 80, 60, 120);
        ctx.strokeStyle = '#808080';
        ctx.lineWidth = 2;
        ctx.strokeRect(320, 80, 60, 120);
        ctx.strokeRect(320, 140, 60, 2);

        // 绘制炉灶
        ctx.fillStyle = '#000';
        ctx.fillRect(100, 140, 80, 15);
        ctx.fillStyle = '#FF0000';
        ctx.beginPath();
        ctx.arc(120, 147, 8, 0, 2 * Math.PI);
        ctx.fill();
        ctx.beginPath();
        ctx.arc(160, 147, 8, 0, 2 * Math.PI);
        ctx.fill();

        // 绘制水槽
        ctx.fillStyle = '#C0C0C0';
        ctx.fillRect(220, 140, 60, 15);
        ctx.strokeStyle = '#808080';
        ctx.lineWidth = 1;
        ctx.strokeRect(220, 140, 60, 15);

        // 添加文字说明
        ctx.fillStyle = '#333';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('现代化厨房设计', 200, 280);
    }

    // 绘制卫生间装修效果
    async drawBathroomDesign() {
        const ctx = this.resultCtx;

        // 绘制地板
        ctx.fillStyle = '#E6E6FA';
        ctx.fillRect(0, 200, 400, 100);

        // 绘制墙面瓷砖
        ctx.fillStyle = '#F0F8FF';
        ctx.fillRect(0, 0, 400, 200);

        // 绘制瓷砖纹理
        ctx.strokeStyle = '#D3D3D3';
        ctx.lineWidth = 1;
        for (let i = 0; i < 400; i += 40) {
            ctx.beginPath();
            ctx.moveTo(i, 0);
            ctx.lineTo(i, 200);
            ctx.stroke();
        }
        for (let i = 0; i < 200; i += 40) {
            ctx.beginPath();
            ctx.moveTo(0, i);
            ctx.lineTo(400, i);
            ctx.stroke();
        }

        // 绘制浴缸
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(50, 120, 120, 80);
        ctx.strokeStyle = '#C0C0C0';
        ctx.lineWidth = 3;
        ctx.strokeRect(50, 120, 120, 80);

        // 绘制马桶
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(200, 160, 40, 50);
        ctx.fillRect(205, 150, 30, 20);

        // 绘制洗手台
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(280, 140, 80, 60);
        ctx.fillStyle = '#C0C0C0';
        ctx.fillRect(285, 135, 70, 10);

        // 绘制镜子
        ctx.fillStyle = '#E6E6FA';
        ctx.fillRect(290, 80, 60, 50);
        ctx.strokeStyle = '#C0C0C0';
        ctx.lineWidth = 2;
        ctx.strokeRect(290, 80, 60, 50);

        // 添加文字说明
        ctx.fillStyle = '#333';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('简洁卫生间设计', 200, 280);
    }

    // 绘制阳台装修效果
    async drawBalconyDesign() {
        const ctx = this.resultCtx;

        // 绘制地板
        ctx.fillStyle = '#D2B48C';
        ctx.fillRect(0, 200, 400, 100);

        // 绘制天空背景
        const skyGradient = ctx.createLinearGradient(0, 0, 0, 200);
        skyGradient.addColorStop(0, '#87CEEB');
        skyGradient.addColorStop(1, '#E0F6FF');
        ctx.fillStyle = skyGradient;
        ctx.fillRect(0, 0, 400, 200);

        // 绘制栏杆
        ctx.strokeStyle = '#8B4513';
        ctx.lineWidth = 4;
        ctx.beginPath();
        ctx.moveTo(0, 180);
        ctx.lineTo(400, 180);
        ctx.stroke();

        for (let i = 20; i < 400; i += 30) {
            ctx.beginPath();
            ctx.moveTo(i, 180);
            ctx.lineTo(i, 200);
            ctx.stroke();
        }

        // 绘制花盆
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(50, 170, 30, 30);
        ctx.fillRect(150, 170, 30, 30);
        ctx.fillRect(250, 170, 30, 30);

        // 绘制植物
        ctx.fillStyle = '#228B22';
        ctx.fillRect(55, 150, 20, 25);
        ctx.fillRect(155, 150, 20, 25);
        ctx.fillRect(255, 150, 20, 25);

        // 绘制椅子
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(320, 160, 40, 40);
        ctx.fillRect(325, 140, 30, 25);

        // 绘制云朵
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(100, 50, 20, 0, 2 * Math.PI);
        ctx.arc(120, 45, 25, 0, 2 * Math.PI);
        ctx.arc(140, 50, 20, 0, 2 * Math.PI);
        ctx.fill();

        // 添加文字说明
        ctx.fillStyle = '#333';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('休闲阳台设计', 200, 280);
    }

    // 绘制默认房间设计
    async drawDefaultRoomDesign(room) {
        const ctx = this.resultCtx;

        // 绘制地板
        ctx.fillStyle = '#DEB887';
        ctx.fillRect(0, 200, 400, 100);

        // 绘制墙面
        const wallGradient = ctx.createLinearGradient(0, 0, 0, 200);
        wallGradient.addColorStop(0, '#F5F5DC');
        wallGradient.addColorStop(1, room.color + '40');
        ctx.fillStyle = wallGradient;
        ctx.fillRect(0, 0, 400, 200);

        // 绘制简单家具
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(100, 150, 80, 50);
        ctx.fillRect(220, 160, 60, 40);

        // 添加文字说明
        ctx.fillStyle = '#333';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`${room.name}装修设计`, 200, 280);
    }
    
    // 保存结果图片
    saveResultImage() {
        if (!this.resultCanvas) {
            this.showMessage('没有可保存的图片！', 'warning');
            return;
        }
        
        try {
            const link = document.createElement('a');
            link.download = `${this.selectedRoom?.name || '房间'}_装修效果图.png`;
            link.href = this.resultCanvas.toDataURL();
            link.click();
            
            this.showMessage('图片保存成功！', 'success');
        } catch (error) {
            console.error('保存图片失败:', error);
            this.showMessage('保存图片失败！', 'error');
        }
    }
    
    // 显示消息
    showMessage(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        
        // 这里可以添加更好的消息显示UI
        if (type === 'error') {
            alert('错误: ' + message);
        } else if (type === 'success') {
            alert('成功: ' + message);
        }
    }
}

// 当页面加载完成时启动应用
document.addEventListener('DOMContentLoaded', () => {
    window.floorplanDesigner = new FloorplanDesigner();
});
