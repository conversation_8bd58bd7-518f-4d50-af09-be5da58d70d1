// 主应用程序类
class FloorplanDesigner {
    constructor() {
        this.currentImage = null;
        this.detectedRooms = [];
        this.selectedRoom = null;
        this.isProcessing = false;
        
        // 房间类型配置
        this.roomTypes = {
            1: { name: '客厅', color: '#4A90E2', type: 'living_room' },
            2: { name: '卧室', color: '#7ED321', type: 'bedroom' },
            3: { name: '厨房', color: '#F5A623', type: 'kitchen' },
            4: { name: '卫生间', color: '#BD10E0', type: 'bathroom' },
            5: { name: '阳台', color: '#FF6B35', type: 'balcony' },
            6: { name: '书房', color: '#8B4513', type: 'study' }
        };
        
        this.initializeApp();
    }
    
    // 初始化应用
    initializeApp() {
        console.log('🏠 房型智能装修设计系统启动中...');
        this.setupEventListeners();
        this.setupCanvases();
        this.showWelcomeMessage();
    }
    
    // 设置事件监听器
    setupEventListeners() {
        const imageInput = document.getElementById('imageInput');
        const uploadArea = document.getElementById('uploadArea');
        const saveBtn = document.getElementById('saveBtn');
        const regenerateBtn = document.getElementById('regenerateBtn');
        
        // 文件选择事件
        imageInput.addEventListener('change', (e) => {
            if (e.target.files && e.target.files[0]) {
                this.handleImageUpload(e.target.files[0]);
            }
        });
        
        // 拖拽上传事件
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files && files[0] && files[0].type.startsWith('image/')) {
                this.handleImageUpload(files[0]);
            }
        });
        
        // 保存按钮事件
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveResultImage();
            });
        }
        
        // 重新生成按钮事件
        if (regenerateBtn) {
            regenerateBtn.addEventListener('click', () => {
                if (this.selectedRoom) {
                    this.generateRoomDesign(this.selectedRoom);
                }
            });
        }
    }
    
    // 设置画布
    setupCanvases() {
        this.floorplanCanvas = document.getElementById('floorplanCanvas');
        this.resultCanvas = document.getElementById('resultCanvas');
        
        if (this.floorplanCanvas) {
            this.floorplanCtx = this.floorplanCanvas.getContext('2d');
            
            // 添加画布点击事件
            this.floorplanCanvas.addEventListener('click', (e) => {
                this.handleCanvasClick(e);
            });
        }
        
        if (this.resultCanvas) {
            this.resultCtx = this.resultCanvas.getContext('2d');
        }
    }
    
    // 显示欢迎信息
    showWelcomeMessage() {
        console.log('✨ 欢迎使用房型智能装修设计系统！');
        console.log('📋 功能说明：');
        console.log('   1. 上传房型图片');
        console.log('   2. AI自动识别房间');
        console.log('   3. 点击房间生成装修效果');
        console.log('💡 完全免费，无需注册！');
    }
    
    // 处理图片上传
    async handleImageUpload(file) {
        if (this.isProcessing) {
            this.showMessage('正在处理中，请稍候...', 'warning');
            return;
        }
        
        // 验证文件类型
        if (!file.type.startsWith('image/')) {
            this.showMessage('请选择有效的图片文件！', 'error');
            return;
        }
        
        // 验证文件大小 (限制10MB)
        if (file.size > 10 * 1024 * 1024) {
            this.showMessage('图片文件过大，请选择小于10MB的图片！', 'error');
            return;
        }
        
        try {
            this.isProcessing = true;
            this.showProcessingStatus('正在加载图片...', 10);
            
            // 读取图片文件
            const imageDataUrl = await this.readFileAsDataURL(file);
            const image = await this.loadImage(imageDataUrl);
            
            this.currentImage = image;
            this.showProcessingStatus('图片加载完成，开始分析房型...', 30);
            
            // 开始房间识别
            await this.processFloorplan(image);
            
        } catch (error) {
            console.error('图片处理失败:', error);
            this.showMessage('图片处理失败，请重试！', 'error');
            this.isProcessing = false;
            this.hideProcessingStatus();
        }
    }
    
    // 读取文件为DataURL
    readFileAsDataURL(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }
    
    // 加载图片
    loadImage(src) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = reject;
            img.src = src;
        });
    }
    
    // 处理房型图
    async processFloorplan(image) {
        try {
            this.showProcessingStatus('正在识别房间结构...', 50);
            
            // 暂时使用模拟数据，后续会替换为真实的AI识别
            await this.simulateRoomDetection(image);
            
            this.showProcessingStatus('房间识别完成！', 100);
            
            // 显示结果
            setTimeout(() => {
                this.displayFloorplanResult();
                this.hideProcessingStatus();
                this.showMainWorkspace();
                this.isProcessing = false;
            }, 1000);
            
        } catch (error) {
            console.error('房型处理失败:', error);
            throw error;
        }
    }
    
    // 模拟房间检测（临时实现）
    async simulateRoomDetection(image) {
        // 模拟AI处理时间
        await this.delay(2000);
        
        // 生成模拟的房间数据
        this.detectedRooms = [
            {
                id: 1,
                type: 'living_room',
                name: '客厅',
                color: '#4A90E2',
                coordinates: [
                    [50, 50], [200, 50], [200, 150], [50, 150]
                ],
                center: [125, 100],
                area: 25
            },
            {
                id: 2,
                type: 'bedroom',
                name: '卧室',
                color: '#7ED321',
                coordinates: [
                    [220, 50], [350, 50], [350, 150], [220, 150]
                ],
                center: [285, 100],
                area: 18
            },
            {
                id: 3,
                type: 'kitchen',
                name: '厨房',
                color: '#F5A623',
                coordinates: [
                    [50, 170], [150, 170], [150, 250], [50, 250]
                ],
                center: [100, 210],
                area: 12
            },
            {
                id: 4,
                type: 'bathroom',
                name: '卫生间',
                color: '#BD10E0',
                coordinates: [
                    [170, 170], [220, 170], [220, 220], [170, 220]
                ],
                center: [195, 195],
                area: 6
            }
        ];
        
        console.log('🏠 检测到房间:', this.detectedRooms.length, '个');
    }
    
    // 延迟函数
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // 显示处理状态
    showProcessingStatus(message, progress) {
        const statusSection = document.getElementById('statusSection');
        const statusTitle = document.getElementById('statusTitle');
        const statusDescription = document.getElementById('statusDescription');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        
        if (statusSection) {
            statusSection.style.display = 'block';
        }
        
        if (statusTitle) {
            statusTitle.textContent = message;
        }
        
        if (statusDescription) {
            statusDescription.textContent = '请耐心等待，AI正在努力工作中...';
        }
        
        if (progressFill) {
            progressFill.style.width = progress + '%';
        }
        
        if (progressText) {
            progressText.textContent = progress + '%';
        }
    }
    
    // 隐藏处理状态
    hideProcessingStatus() {
        const statusSection = document.getElementById('statusSection');
        if (statusSection) {
            statusSection.style.display = 'none';
        }
    }
    
    // 显示主工作区
    showMainWorkspace() {
        const mainWorkspace = document.getElementById('mainWorkspace');
        if (mainWorkspace) {
            mainWorkspace.style.display = 'block';
            mainWorkspace.scrollIntoView({ behavior: 'smooth' });
        }
    }
    
    // 显示房型识别结果
    displayFloorplanResult() {
        if (!this.currentImage || !this.detectedRooms.length) {
            return;
        }
        
        // 设置画布尺寸
        const maxWidth = 400;
        const maxHeight = 300;
        const scale = Math.min(maxWidth / this.currentImage.width, maxHeight / this.currentImage.height);
        
        this.floorplanCanvas.width = this.currentImage.width * scale;
        this.floorplanCanvas.height = this.currentImage.height * scale;
        
        // 绘制原始图片
        this.floorplanCtx.drawImage(
            this.currentImage, 
            0, 0, 
            this.floorplanCanvas.width, 
            this.floorplanCanvas.height
        );
        
        // 绘制房间标注
        this.drawRoomAnnotations(scale);
        
        // 生成房间图例
        this.generateRoomLegend();
        
        console.log('✅ 房型图显示完成');
    }
    
    // 绘制房间标注
    drawRoomAnnotations(scale) {
        this.floorplanCtx.globalAlpha = 0.6;
        
        this.detectedRooms.forEach(room => {
            // 绘制房间区域
            this.floorplanCtx.fillStyle = room.color;
            this.floorplanCtx.beginPath();
            
            room.coordinates.forEach((point, index) => {
                const x = point[0] * scale;
                const y = point[1] * scale;
                
                if (index === 0) {
                    this.floorplanCtx.moveTo(x, y);
                } else {
                    this.floorplanCtx.lineTo(x, y);
                }
            });
            
            this.floorplanCtx.closePath();
            this.floorplanCtx.fill();
            
            // 绘制房间边框
            this.floorplanCtx.globalAlpha = 1;
            this.floorplanCtx.strokeStyle = room.color;
            this.floorplanCtx.lineWidth = 2;
            this.floorplanCtx.stroke();
            
            // 绘制房间标签
            this.floorplanCtx.fillStyle = '#fff';
            this.floorplanCtx.font = 'bold 14px Arial';
            this.floorplanCtx.textAlign = 'center';
            this.floorplanCtx.fillText(
                room.name,
                room.center[0] * scale,
                room.center[1] * scale
            );
            
            this.floorplanCtx.globalAlpha = 0.6;
        });
        
        this.floorplanCtx.globalAlpha = 1;
    }
    
    // 生成房间图例
    generateRoomLegend() {
        const legendContainer = document.getElementById('roomLegend');
        if (!legendContainer) return;
        
        legendContainer.innerHTML = '';
        
        this.detectedRooms.forEach(room => {
            const legendItem = document.createElement('div');
            legendItem.className = 'legend-item';
            
            legendItem.innerHTML = `
                <div class="legend-color" style="background-color: ${room.color}"></div>
                <span>${room.name} (${room.area}㎡)</span>
            `;
            
            legendContainer.appendChild(legendItem);
        });
    }
    
    // 处理画布点击事件
    handleCanvasClick(event) {
        if (!this.detectedRooms.length || this.isProcessing) {
            return;
        }
        
        const rect = this.floorplanCanvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        // 检查点击的是哪个房间
        const clickedRoom = this.findClickedRoom(x, y);
        
        if (clickedRoom) {
            this.selectRoom(clickedRoom);
        }
    }
    
    // 查找被点击的房间
    findClickedRoom(x, y) {
        const scale = this.floorplanCanvas.width / this.currentImage.width;
        
        for (let room of this.detectedRooms) {
            if (this.isPointInPolygon(x / scale, y / scale, room.coordinates)) {
                return room;
            }
        }
        
        return null;
    }
    
    // 检查点是否在多边形内
    isPointInPolygon(x, y, polygon) {
        let inside = false;
        
        for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
            const xi = polygon[i][0], yi = polygon[i][1];
            const xj = polygon[j][0], yj = polygon[j][1];
            
            if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
                inside = !inside;
            }
        }
        
        return inside;
    }
    
    // 选择房间
    async selectRoom(room) {
        this.selectedRoom = room;
        
        // 更新选中房间信息
        const selectedRoomInfo = document.getElementById('selectedRoomInfo');
        if (selectedRoomInfo) {
            selectedRoomInfo.textContent = `正在生成 ${room.name} 的装修效果图...`;
        }
        
        console.log('🎯 选中房间:', room.name);
        
        // 生成装修效果图
        await this.generateRoomDesign(room);
    }
    
    // 生成房间装修设计
    async generateRoomDesign(room) {
        try {
            this.isProcessing = true;
            
            // 隐藏占位符
            const placeholder = document.getElementById('resultPlaceholder');
            if (placeholder) {
                placeholder.style.display = 'none';
            }
            
            // 更新状态信息
            const selectedRoomInfo = document.getElementById('selectedRoomInfo');
            if (selectedRoomInfo) {
                selectedRoomInfo.textContent = `正在为 ${room.name} 生成装修效果图...`;
            }
            
            // 暂时使用模拟生成，后续替换为真实AI
            await this.simulateDesignGeneration(room);
            
            // 显示操作按钮
            const resultActions = document.getElementById('resultActions');
            if (resultActions) {
                resultActions.style.display = 'flex';
            }
            
            // 更新状态信息
            if (selectedRoomInfo) {
                selectedRoomInfo.textContent = `${room.name} 装修效果图`;
            }
            
            this.isProcessing = false;
            
        } catch (error) {
            console.error('装修效果图生成失败:', error);
            this.showMessage('装修效果图生成失败，请重试！', 'error');
            this.isProcessing = false;
        }
    }
    
    // 模拟装修效果生成
    async simulateDesignGeneration(room) {
        // 模拟处理时间
        await this.delay(3000);
        
        // 在结果画布上绘制模拟效果图
        this.resultCanvas.width = 400;
        this.resultCanvas.height = 300;
        
        // 绘制背景
        const gradient = this.resultCtx.createLinearGradient(0, 0, 400, 300);
        gradient.addColorStop(0, room.color + '40');
        gradient.addColorStop(1, room.color + '80');
        
        this.resultCtx.fillStyle = gradient;
        this.resultCtx.fillRect(0, 0, 400, 300);
        
        // 绘制装修效果文字
        this.resultCtx.fillStyle = '#333';
        this.resultCtx.font = 'bold 24px Arial';
        this.resultCtx.textAlign = 'center';
        this.resultCtx.fillText(`${room.name}装修效果图`, 200, 150);
        
        this.resultCtx.font = '16px Arial';
        this.resultCtx.fillText('(AI生成的装修效果)', 200, 180);
        
        console.log('🎨 装修效果图生成完成');
    }
    
    // 保存结果图片
    saveResultImage() {
        if (!this.resultCanvas) {
            this.showMessage('没有可保存的图片！', 'warning');
            return;
        }
        
        try {
            const link = document.createElement('a');
            link.download = `${this.selectedRoom?.name || '房间'}_装修效果图.png`;
            link.href = this.resultCanvas.toDataURL();
            link.click();
            
            this.showMessage('图片保存成功！', 'success');
        } catch (error) {
            console.error('保存图片失败:', error);
            this.showMessage('保存图片失败！', 'error');
        }
    }
    
    // 显示消息
    showMessage(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        
        // 这里可以添加更好的消息显示UI
        if (type === 'error') {
            alert('错误: ' + message);
        } else if (type === 'success') {
            alert('成功: ' + message);
        }
    }
}

// 当页面加载完成时启动应用
document.addEventListener('DOMContentLoaded', () => {
    window.floorplanDesigner = new FloorplanDesigner();
});
