<thought>
  <exploration>
    ## 系统架构思维探索
    
    ### 架构设计维度
    - **业务架构**：领域建模、业务流程、服务边界
    - **应用架构**：分层设计、模块划分、接口定义
    - **数据架构**：数据模型、存储策略、数据流向
    - **技术架构**：技术选型、部署架构、运维策略
    
    ### 架构演进路径
    - **单体架构**：快速开发、简单部署、适合小团队
    - **分层架构**：职责分离、可维护性、技术栈解耦
    - **微服务架构**：独立部署、技术多样性、团队自治
    - **云原生架构**：弹性扩展、容器化、服务网格
    
    ### 非功能性需求考虑
    - **性能要求**：响应时间、吞吐量、并发用户数
    - **可用性要求**：系统稳定性、故障恢复、灾备策略
    - **扩展性要求**：水平扩展、垂直扩展、弹性伸缩
    - **安全性要求**：身份认证、授权控制、数据保护
  </exploration>
  
  <reasoning>
    ## 架构决策推理
    
    ### 架构选择决策树
    ```
    业务复杂度评估
    ├── 简单业务 → 单体架构 + 分层设计
    ├── 中等复杂度 → 模块化单体 + 领域驱动
    └── 高复杂度 → 微服务架构 + 分布式设计
    
    团队规模考虑
    ├── 小团队(≤5人) → 单体架构优先
    ├── 中等团队(5-20人) → 模块化架构
    └── 大团队(>20人) → 微服务架构
    ```
    
    ### 技术选型推理
    - **前端框架选择**：基于团队技能、项目复杂度、生态支持
    - **后端语言选择**：基于性能要求、开发效率、团队熟悉度
    - **数据库选择**：基于数据特征、查询模式、一致性要求
    - **部署方式选择**：基于运维能力、成本预算、扩展需求
    
    ### 性能优化策略
    - **前端优化**：代码分割、懒加载、CDN、缓存策略
    - **后端优化**：算法优化、数据库优化、缓存设计、异步处理
    - **网络优化**：HTTP/2、压缩、连接池、负载均衡
    - **存储优化**：索引设计、分库分表、读写分离、数据归档
  </reasoning>
  
  <challenge>
    ## 架构设计挑战
    
    ### 复杂性管理
    - 如何控制系统复杂度避免过度设计？
    - 如何在架构演进中保持系统一致性？
    - 如何处理遗留系统的技术债务？
    
    ### 分布式系统挑战
    - 如何处理网络分区和服务故障？
    - 如何保证分布式事务的一致性？
    - 如何设计有效的服务治理策略？
    
    ### 技术选型风险
    - 如何评估新技术的成熟度和风险？
    - 如何平衡技术先进性和稳定性？
    - 如何处理技术栈的版本升级？
  </challenge>
  
  <plan>
    ## 架构设计规划
    
    ### 架构设计流程
    1. **需求分析**：功能需求、非功能需求、约束条件
    2. **架构建模**：概念架构、逻辑架构、物理架构
    3. **技术选型**：框架选择、工具选择、平台选择
    4. **原型验证**：关键技术验证、性能测试、可行性验证
    5. **架构文档**：设计文档、部署文档、运维文档
    
    ### 架构治理计划
    - **架构评审**：定期评审架构设计和实现质量
    - **技术演进**：制定技术升级和架构演进计划
    - **知识传承**：建立架构知识库和最佳实践
    - **团队培养**：提升团队架构设计和实现能力
  </plan>
</thought>
