{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-06-21T15:08:13.660Z", "updatedAt": "2025-06-21T15:08:13.662Z", "resourceCount": 10}, "resources": [{"id": "fullstack-engineer", "source": "project", "protocol": "role", "name": "Fullstack Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/fullstack-engineer/fullstack-engineer.role.md", "metadata": {"createdAt": "2025-06-21T15:08:13.661Z", "updatedAt": "2025-06-21T15:08:13.661Z", "scannedAt": "2025-06-21T15:08:13.661Z"}}, {"id": "fullstack-thinking", "source": "project", "protocol": "thought", "name": "Fullstack Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/fullstack-engineer/thought/fullstack-thinking.thought.md", "metadata": {"createdAt": "2025-06-21T15:08:13.661Z", "updatedAt": "2025-06-21T15:08:13.661Z", "scannedAt": "2025-06-21T15:08:13.661Z"}}, {"id": "system-architecture", "source": "project", "protocol": "thought", "name": "System Architecture 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/fullstack-engineer/thought/system-architecture.thought.md", "metadata": {"createdAt": "2025-06-21T15:08:13.661Z", "updatedAt": "2025-06-21T15:08:13.661Z", "scannedAt": "2025-06-21T15:08:13.661Z"}}, {"id": "code-quality", "source": "project", "protocol": "execution", "name": "Code Quality 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/fullstack-engineer/execution/code-quality.execution.md", "metadata": {"createdAt": "2025-06-21T15:08:13.661Z", "updatedAt": "2025-06-21T15:08:13.661Z", "scannedAt": "2025-06-21T15:08:13.661Z"}}, {"id": "fullstack-development", "source": "project", "protocol": "execution", "name": "Fullstack Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/fullstack-engineer/execution/fullstack-development.execution.md", "metadata": {"createdAt": "2025-06-21T15:08:13.662Z", "updatedAt": "2025-06-21T15:08:13.662Z", "scannedAt": "2025-06-21T15:08:13.661Z"}}, {"id": "system-integration", "source": "project", "protocol": "execution", "name": "System Integration 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/fullstack-engineer/execution/system-integration.execution.md", "metadata": {"createdAt": "2025-06-21T15:08:13.662Z", "updatedAt": "2025-06-21T15:08:13.662Z", "scannedAt": "2025-06-21T15:08:13.662Z"}}, {"id": "backend-technologies", "source": "project", "protocol": "knowledge", "name": "Backend Technologies 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/fullstack-engineer/knowledge/backend-technologies.knowledge.md", "metadata": {"createdAt": "2025-06-21T15:08:13.662Z", "updatedAt": "2025-06-21T15:08:13.662Z", "scannedAt": "2025-06-21T15:08:13.662Z"}}, {"id": "database-systems", "source": "project", "protocol": "knowledge", "name": "Database Systems 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/fullstack-engineer/knowledge/database-systems.knowledge.md", "metadata": {"createdAt": "2025-06-21T15:08:13.662Z", "updatedAt": "2025-06-21T15:08:13.662Z", "scannedAt": "2025-06-21T15:08:13.662Z"}}, {"id": "devops-practices", "source": "project", "protocol": "knowledge", "name": "Devops Practices 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/fullstack-engineer/knowledge/devops-practices.knowledge.md", "metadata": {"createdAt": "2025-06-21T15:08:13.662Z", "updatedAt": "2025-06-21T15:08:13.662Z", "scannedAt": "2025-06-21T15:08:13.662Z"}}, {"id": "frontend-technologies", "source": "project", "protocol": "knowledge", "name": "Frontend Technologies 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/fullstack-engineer/knowledge/frontend-technologies.knowledge.md", "metadata": {"createdAt": "2025-06-21T15:08:13.662Z", "updatedAt": "2025-06-21T15:08:13.662Z", "scannedAt": "2025-06-21T15:08:13.662Z"}}], "stats": {"totalResources": 10, "byProtocol": {"role": 1, "thought": 2, "execution": 3, "knowledge": 4}, "bySource": {"project": 10}}}