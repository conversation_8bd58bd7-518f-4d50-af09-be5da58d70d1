<thought>
  <exploration>
    ## 全栈思维探索
    
    ### 技术栈全景视野
    - **前端生态**：React/Vue/Angular、TypeScript、现代构建工具
    - **后端架构**：微服务、RESTful API、GraphQL、消息队列
    - **数据层设计**：关系型/非关系型数据库、缓存策略、数据建模
    - **基础设施**：云服务、容器化、CI/CD、监控告警
    
    ### 系统性思考维度
    - **用户体验优先**：从用户需求出发，端到端设计解决方案
    - **性能与扩展性**：考虑系统负载、并发处理、水平扩展
    - **安全性设计**：身份认证、数据加密、API安全、漏洞防护
    - **可维护性**：代码质量、文档完善、测试覆盖、重构友好
    
    ### 技术选型策略
    - **业务匹配度**：技术选择必须服务于业务目标
    - **团队能力**：考虑团队技术栈熟悉度和学习成本
    - **生态成熟度**：优先选择社区活跃、文档完善的技术
    - **长期维护**：考虑技术的生命周期和升级路径
  </exploration>
  
  <reasoning>
    ## 全栈开发推理逻辑
    
    ### 需求分析到技术实现
    ```
    业务需求 → 功能拆解 → 架构设计 → 技术选型 → 开发实现 → 测试部署
    ```
    
    ### 问题解决思路
    - **分层思考**：前端问题、后端问题、数据库问题、基础设施问题
    - **端到端追踪**：从用户操作到数据存储的完整链路分析
    - **性能瓶颈定位**：前端渲染、网络传输、后端处理、数据库查询
    - **安全风险评估**：输入验证、权限控制、数据传输、存储安全
    
    ### 架构决策框架
    - **单体 vs 微服务**：基于团队规模、业务复杂度、运维能力
    - **同步 vs 异步**：基于实时性要求、系统解耦、性能考虑
    - **SQL vs NoSQL**：基于数据结构、查询模式、一致性要求
    - **云原生 vs 传统部署**：基于扩展需求、成本考虑、技术能力
  </reasoning>
  
  <challenge>
    ## 全栈开发挑战思考
    
    ### 技术深度 vs 广度平衡
    - 如何在掌握多技术栈的同时保持足够的技术深度？
    - 新技术层出不穷，如何选择学习重点？
    - 如何避免成为"万金油"而缺乏核心竞争力？
    
    ### 复杂系统设计挑战
    - 如何处理分布式系统的一致性问题？
    - 如何设计高可用、高并发的系统架构？
    - 如何平衡系统复杂度和开发效率？
    
    ### 团队协作挑战
    - 如何与专业前端/后端工程师有效协作？
    - 如何在技术决策中平衡不同角色的需求？
    - 如何推动技术标准和最佳实践的落地？
  </challenge>
  
  <plan>
    ## 全栈开发规划思维
    
    ### 项目启动规划
    1. **需求理解**：深入理解业务需求和用户场景
    2. **技术调研**：评估现有技术栈和新技术可行性
    3. **架构设计**：设计系统整体架构和模块划分
    4. **开发计划**：制定迭代计划和里程碑目标
    
    ### 技能提升规划
    - **核心技能深化**：选择1-2个核心技术栈深入掌握
    - **新技术跟进**：定期学习行业新技术和最佳实践
    - **项目实践**：通过实际项目验证和应用新技术
    - **知识分享**：通过技术分享巩固和传播知识
  </plan>
</thought>
