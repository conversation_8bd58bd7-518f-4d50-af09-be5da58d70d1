<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏠 房型智能装修设计 - 完全免费</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <!-- 头部标题 -->
        <header class="header">
            <h1>🏠 房型智能装修设计</h1>
            <p class="subtitle">上传房型图 → 自动识别房间 → 点击生成装修效果图</p>
            <div class="free-badge">✨ 完全免费使用</div>
        </header>

        <!-- 上传区域 -->
        <section class="upload-section">
            <div class="upload-area" id="uploadArea">
                <input type="file" id="imageInput" accept="image/*" style="display: none;">
                <div class="upload-content">
                    <div class="upload-icon">📁</div>
                    <h3>选择房型图片</h3>
                    <p>支持 JPG、PNG、BMP 格式</p>
                    <button class="upload-btn" onclick="document.getElementById('imageInput').click()">
                        选择文件
                    </button>
                </div>
            </div>
        </section>

        <!-- 处理状态显示 -->
        <section class="status-section" id="statusSection" style="display: none;">
            <div class="status-card">
                <div class="status-icon" id="statusIcon">🔄</div>
                <h3 id="statusTitle">正在处理图片...</h3>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <span class="progress-text" id="progressText">0%</span>
                </div>
                <p class="status-description" id="statusDescription">
                    正在分析房型结构，请稍候...
                </p>
            </div>
        </section>

        <!-- 主要工作区域 -->
        <section class="main-workspace" id="mainWorkspace" style="display: none;">
            <div class="workspace-grid">
                <!-- 左侧：房型图显示 -->
                <div class="floorplan-panel">
                    <div class="panel-header">
                        <h3>🏠 房型图</h3>
                        <p>点击不同颜色的房间查看装修效果</p>
                    </div>
                    <div class="canvas-container">
                        <canvas id="floorplanCanvas"></canvas>
                    </div>
                    <div class="room-legend" id="roomLegend">
                        <!-- 房间图例将在这里动态生成 -->
                    </div>
                </div>

                <!-- 右侧：装修效果图显示 -->
                <div class="result-panel">
                    <div class="panel-header">
                        <h3>🎨 装修效果图</h3>
                        <p id="selectedRoomInfo">请点击左侧房间查看效果</p>
                    </div>
                    <div class="canvas-container">
                        <canvas id="resultCanvas"></canvas>
                        <div class="placeholder" id="resultPlaceholder">
                            <div class="placeholder-icon">🖼️</div>
                            <p>点击房间后，装修效果图将显示在这里</p>
                        </div>
                    </div>
                    <div class="result-actions" id="resultActions" style="display: none;">
                        <button class="action-btn save-btn" id="saveBtn">
                            💾 保存图片
                        </button>
                        <button class="action-btn regenerate-btn" id="regenerateBtn">
                            🔄 重新生成
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 使用说明 -->
        <section class="instructions">
            <h3>📋 使用说明</h3>
            <div class="instruction-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h4>上传房型图</h4>
                        <p>选择清晰的房型平面图，确保房间边界清楚</p>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h4>自动识别房间</h4>
                        <p>AI将自动识别不同房间并用颜色标注</p>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h4>点击查看效果</h4>
                        <p>点击任意房间，即可生成该房间的装修效果图</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 免费使用说明 -->
        <section class="free-info">
            <div class="free-card">
                <h3>💡 完全免费使用</h3>
                <ul>
                    <li>✅ 无需注册登录</li>
                    <li>✅ 数据本地存储，保护隐私</li>
                    <li>✅ 每日免费AI渲染次数充足</li>
                    <li>✅ 开源项目，可自行部署</li>
                </ul>
            </div>
        </section>

        <!-- 页脚 -->
        <footer class="footer">
            <p>🏠 房型智能装修设计 | 基于AI技术 | 完全免费开源</p>
        </footer>
    </div>

    <!-- 引入TensorFlow.js -->
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js"></script>
    
    <!-- 引入主要JavaScript文件 -->
    <script src="js/main.js"></script>
</body>
</html>
