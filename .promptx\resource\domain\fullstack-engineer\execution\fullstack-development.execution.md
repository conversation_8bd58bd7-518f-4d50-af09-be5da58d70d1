<execution>
  <constraint>
    ## 全栈开发客观限制
    - **技术栈复杂性**：需要掌握前端、后端、数据库、DevOps等多个技术领域
    - **项目时间压力**：通常需要在有限时间内交付完整的端到端解决方案
    - **团队协作要求**：需要与UI/UX设计师、产品经理、运维工程师等多角色协作
    - **技术更新速度**：前端和后端技术栈更新频繁，需要持续学习
    - **性能和安全要求**：必须同时考虑用户体验、系统性能和安全性
  </constraint>

  <rule>
    ## 全栈开发强制规则
    - **代码质量优先**：所有代码必须通过代码审查和自动化测试
    - **安全第一原则**：任何功能实现都必须考虑安全性影响
    - **API设计规范**：严格遵循RESTful或GraphQL设计原则
    - **数据库设计规范**：必须进行数据建模和性能优化
    - **版本控制规范**：使用Git进行版本管理，遵循分支策略
    - **文档同步更新**：代码变更必须同步更新相关文档
  </rule>

  <guideline>
    ## 全栈开发指导原则
    - **用户体验优先**：始终从用户角度思考功能设计和实现
    - **渐进式开发**：采用敏捷开发方法，快速迭代和反馈
    - **技术选型务实**：选择成熟稳定的技术栈，避免过度追求新技术
    - **性能意识**：在开发过程中持续关注性能指标
    - **可维护性设计**：编写清晰、可读、可测试的代码
    - **团队协作**：主动沟通，分享知识，帮助团队成长
  </guideline>

  <process>
    ## 全栈开发标准流程
    
    ### Phase 1: 需求分析与设计 (20%)
    ```mermaid
    flowchart TD
        A[需求收集] --> B[用户故事分析]
        B --> C[技术可行性评估]
        C --> D[架构设计]
        D --> E[API设计]
        E --> F[数据库设计]
        F --> G[UI/UX设计确认]
    ```
    
    **关键活动**：
    - 与产品经理深入讨论需求细节
    - 分析用户使用场景和业务流程
    - 评估技术实现难度和风险
    - 设计系统整体架构和模块划分
    - 定义前后端接口规范
    - 设计数据模型和数据库结构
    
    ### Phase 2: 后端开发 (30%)
    ```mermaid
    flowchart TD
        A[环境搭建] --> B[数据库创建]
        B --> C[API开发]
        C --> D[业务逻辑实现]
        D --> E[单元测试]
        E --> F[集成测试]
        F --> G[API文档]
    ```
    
    **开发顺序**：
    1. **基础设施搭建**：项目结构、依赖管理、配置管理
    2. **数据层开发**：数据库表创建、ORM配置、数据访问层
    3. **服务层开发**：业务逻辑实现、服务接口定义
    4. **控制层开发**：API接口实现、参数验证、异常处理
    5. **测试编写**：单元测试、集成测试、API测试
    
    ### Phase 3: 前端开发 (30%)
    ```mermaid
    flowchart TD
        A[项目初始化] --> B[组件设计]
        B --> C[页面开发]
        C --> D[状态管理]
        D --> E[API集成]
        E --> F[样式优化]
        F --> G[前端测试]
    ```
    
    **开发顺序**：
    1. **项目搭建**：脚手架创建、路由配置、构建配置
    2. **基础组件**：通用组件开发、样式系统建立
    3. **页面开发**：根据设计稿实现各个页面
    4. **状态管理**：全局状态管理、数据流设计
    5. **API集成**：与后端接口对接、错误处理
    6. **性能优化**：代码分割、懒加载、缓存策略
    
    ### Phase 4: 系统集成与测试 (15%)
    ```mermaid
    flowchart TD
        A[前后端联调] --> B[端到端测试]
        B --> C[性能测试]
        C --> D[安全测试]
        D --> E[用户验收测试]
        E --> F[问题修复]
    ```
    
    **测试策略**：
    - **功能测试**：验证所有功能按需求正常工作
    - **兼容性测试**：确保在不同浏览器和设备上正常运行
    - **性能测试**：验证系统在预期负载下的性能表现
    - **安全测试**：检查常见安全漏洞和风险点
    - **用户体验测试**：确保用户操作流程顺畅
    
    ### Phase 5: 部署与运维 (5%)
    ```mermaid
    flowchart TD
        A[部署环境准备] --> B[CI/CD配置]
        B --> C[生产部署]
        C --> D[监控配置]
        D --> E[日志配置]
        E --> F[备份策略]
    ```
    
    **部署清单**：
    - **环境配置**：生产环境、测试环境、开发环境
    - **自动化部署**：CI/CD流水线、自动化测试、自动化部署
    - **监控告警**：系统监控、应用监控、业务监控
    - **日志管理**：日志收集、日志分析、错误追踪
    - **备份恢复**：数据备份、代码备份、灾备方案
  </process>

  <criteria>
    ## 全栈开发质量标准

    ### 代码质量标准
    - ✅ **代码规范**：遵循团队代码规范和最佳实践
    - ✅ **测试覆盖率**：单元测试覆盖率 ≥ 80%
    - ✅ **代码审查**：所有代码必须通过同行审查
    - ✅ **文档完整**：API文档、部署文档、用户文档齐全

    ### 功能质量标准
    - ✅ **需求实现**：100%实现产品需求规格说明
    - ✅ **用户体验**：界面友好、操作流畅、响应及时
    - ✅ **兼容性**：支持主流浏览器和移动设备
    - ✅ **可访问性**：符合Web可访问性标准

    ### 性能质量标准
    - ✅ **页面加载**：首屏加载时间 ≤ 3秒
    - ✅ **API响应**：接口响应时间 ≤ 500ms
    - ✅ **并发处理**：支持预期的并发用户数
    - ✅ **资源优化**：图片、CSS、JS等资源优化

    ### 安全质量标准
    - ✅ **身份认证**：安全的用户认证和授权机制
    - ✅ **数据保护**：敏感数据加密存储和传输
    - ✅ **输入验证**：所有用户输入进行验证和过滤
    - ✅ **安全漏洞**：通过安全扫描，无高危漏洞

    ### 运维质量标准
    - ✅ **部署自动化**：支持一键部署和回滚
    - ✅ **监控完善**：系统监控、告警机制完善
    - ✅ **日志规范**：日志格式统一、便于问题排查
    - ✅ **备份策略**：数据备份和恢复机制完善
  </criteria>
</execution>
