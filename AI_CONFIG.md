# 🤖 AI装修效果图生成配置指南

## 🎯 零成本AI服务配置

### 方案1: Hugging Face 免费API
1. **注册账号**: 访问 [huggingface.co](https://huggingface.co) 免费注册
2. **获取Token**: 
   - 进入 Settings → Access Tokens
   - 创建新的 Read token
   - 复制token到 `js/main.js` 第556行
3. **免费额度**: 每月有一定免费调用次数

```javascript
// 在 js/main.js 中替换这行：
'Authorization': 'Bearer hf_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
// 改为你的真实token：
'Authorization': 'Bearer hf_your_real_token_here',
```

### 方案2: DeepAI 免费API
1. **注册账号**: 访问 [deepai.org](https://deepai.org) 
2. **获取API Key**: 在Dashboard中获取免费API密钥
3. **免费额度**: 每月500次免费调用

### 方案3: 本地Stable Diffusion
1. **安装**: 下载 [AUTOMATIC1111 WebUI](https://github.com/AUTOMATIC1111/stable-diffusion-webui)
2. **启动**: 运行 `webui-user.bat` (Windows) 或 `webui.sh` (Linux/Mac)
3. **配置**: 确保在 `http://127.0.0.1:7860` 运行
4. **优势**: 完全免费，无限制使用

## 🚀 推荐配置步骤

### 快速开始（推荐）
1. 直接使用当前版本 - 已配置高质量本地生成
2. 如需真实AI效果，按上述方案配置API

### 高级配置
1. 同时配置多个AI服务作为备用
2. 根据网络情况自动选择最佳服务
3. 本地Stable Diffusion作为最终备用

## 🎨 效果图质量说明

### 当前版本特色
- ✅ **高质量本地渲染**: 精美的Canvas绘制效果
- ✅ **多种房间类型**: 客厅、卧室、厨房、卫生间、阳台
- ✅ **3D视觉效果**: 透视、光影、材质纹理
- ✅ **零成本使用**: 无需任何API密钥即可使用

### AI增强版本
- 🤖 **真实AI生成**: 使用Stable Diffusion等AI模型
- 🎯 **个性化设计**: 根据房间特点生成独特设计
- 🌟 **照片级质量**: 接近真实装修照片的效果

## 🔧 技术实现说明

### 当前架构
```
用户上传图片 → 房间识别 → 点击房间 → 生成效果图
                                    ↓
                            1. 尝试AI服务
                            2. 备用高质量本地生成
                            3. 基础本地生成
```

### 扩展性设计
- 支持多种AI服务无缝切换
- 渐进式降级保证用户体验
- 模块化设计便于添加新服务

## 📝 使用说明

1. **直接使用**: 当前版本无需配置，直接使用高质量本地生成
2. **AI增强**: 配置API后可获得真实AI生成效果
3. **混合模式**: AI服务失败时自动使用本地生成

## 🎯 未来规划

- [ ] 集成更多免费AI服务
- [ ] 支持用户自定义装修风格
- [ ] 添加装修材料和色彩选择
- [ ] 支持多角度效果图生成
- [ ] 集成VR/AR预览功能
